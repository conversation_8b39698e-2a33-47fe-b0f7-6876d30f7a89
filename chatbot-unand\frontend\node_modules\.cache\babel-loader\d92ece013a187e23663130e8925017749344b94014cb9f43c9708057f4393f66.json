{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 13v6h-6\",\n  key: \"1hxl6d\"\n}], [\"path\", {\n  d: \"M5 11V5h6\",\n  key: \"12e2xe\"\n}], [\"path\", {\n  d: \"m5 5 14 14\",\n  key: \"11anup\"\n}]];\nconst MoveDiagonal2 = createLucideIcon(\"move-diagonal-2\", __iconNode);\nexport { __iconNode, MoveDiagonal2 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MoveDiagonal2", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\move-diagonal-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 13v6h-6', key: '1hxl6d' }],\n  ['path', { d: 'M5 11V5h6', key: '12e2xe' }],\n  ['path', { d: 'm5 5 14 14', key: '11anup' }],\n];\n\n/**\n * @component @name MoveDiagonal2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTN2NmgtNiIgLz4KICA8cGF0aCBkPSJNNSAxMVY1aDYiIC8+CiAgPHBhdGggZD0ibTUgNSAxNCAxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/move-diagonal-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MoveDiagonal2 = createLucideIcon('move-diagonal-2', __iconNode);\n\nexport default MoveDiagonal2;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}