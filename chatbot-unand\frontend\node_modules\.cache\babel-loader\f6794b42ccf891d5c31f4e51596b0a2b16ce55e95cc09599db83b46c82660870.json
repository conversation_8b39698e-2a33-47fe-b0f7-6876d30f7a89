{"ast": null, "code": "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\"a\", \"button\", \"div\", \"form\", \"h2\", \"h3\", \"img\", \"input\", \"label\", \"li\", \"nav\", \"ol\", \"p\", \"select\", \"span\", \"svg\", \"ul\"];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const {\n      asChild,\n      ...primitiveProps\n    } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */jsx(Comp, {\n      ...primitiveProps,\n      ref: forwardedRef\n    });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return {\n    ...primitive,\n    [node]: Node\n  };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport { Primitive, Root, dispatchDiscreteCustomEvent };", "map": {"version": 3, "names": ["React", "ReactDOM", "createSlot", "jsx", "NODES", "Primitive", "reduce", "primitive", "node", "Slot", "Node", "forwardRef", "props", "forwardedRef", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "window", "Symbol", "for", "ref", "displayName", "dispatchDiscreteCustomEvent", "target", "event", "flushSync", "dispatchEvent", "Root"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-primitive\\src\\primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AACvB,YAAYC,QAAA,MAAc;AAC1B,SAASC,UAAA,QAAkB;AA4ChB,SAAAC,GAAA;AA1CX,IAAMC,KAAA,GAAQ,CACZ,KACA,UACA,OACA,QACA,MACA,MACA,OACA,SACA,SACA,MACA,OACA,MACA,KACA,UACA,QACA,OACA,KACF;AAcA,IAAMC,SAAA,GAAYD,KAAA,CAAME,MAAA,CAAO,CAACC,SAAA,EAAWC,IAAA,KAAS;EAClD,MAAMC,IAAA,GAAOP,UAAA,CAAW,aAAaM,IAAI,EAAE;EAC3C,MAAME,IAAA,GAAaV,KAAA,CAAAW,UAAA,CAAW,CAACC,KAAA,EAA2CC,YAAA,KAAsB;IAC9F,MAAM;MAAEC,OAAA;MAAS,GAAGC;IAAe,IAAIH,KAAA;IACvC,MAAMI,IAAA,GAAYF,OAAA,GAAUL,IAAA,GAAOD,IAAA;IAEnC,IAAI,OAAOS,MAAA,KAAW,aAAa;MAChCA,MAAA,CAAeC,MAAA,CAAOC,GAAA,CAAI,UAAU,CAAC,IAAI;IAC5C;IAEA,OAAO,eAAAhB,GAAA,CAACa,IAAA;MAAM,GAAGD,cAAA;MAAgBK,GAAA,EAAKP;IAAA,CAAc;EACtD,CAAC;EAEDH,IAAA,CAAKW,WAAA,GAAc,aAAab,IAAI;EAEpC,OAAO;IAAE,GAAGD,SAAA;IAAW,CAACC,IAAI,GAAGE;EAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAASY,4BAAmDC,MAAA,EAAqBC,KAAA,EAAU;EACzF,IAAID,MAAA,EAAiBtB,QAAA,CAAAwB,SAAA,CAAU,MAAMF,MAAA,CAAOG,aAAA,CAAcF,KAAK,CAAC;AAClE;AAIA,IAAMG,IAAA,GAAOtB,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}