from sqlalchemy.orm import Session
from database import ChatSession, ChatMessage
from datetime import datetime
import uuid
import json
from typing import List, Optional

class ChatService:
    def __init__(self, db: Session):
        self.db = db
    
    def create_session(self, title: str = "New Chat") -> str:
        """Create a new chat session and return session_id"""
        session_id = str(uuid.uuid4())
        db_session = ChatSession(
            session_id=session_id,
            title=title,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        self.db.add(db_session)
        self.db.commit()
        self.db.refresh(db_session)
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get chat session by ID"""
        return self.db.query(ChatSession).filter(
            ChatSession.session_id == session_id,
            ChatSession.is_active == True
        ).first()
    
    def get_all_sessions(self) -> List[ChatSession]:
        """Get all active chat sessions"""
        return self.db.query(ChatSession).filter(
            ChatSession.is_active == True
        ).order_by(ChatSession.updated_at.desc()).all()
    
    def update_session_title(self, session_id: str, title: str) -> bool:
        """Update session title"""
        session = self.get_session(session_id)
        if session:
            session.title = title
            session.updated_at = datetime.utcnow()
            self.db.commit()
            return True
        return False
    
    def delete_session(self, session_id: str) -> bool:
        """Soft delete a session"""
        session = self.get_session(session_id)
        if session:
            session.is_active = False
            session.updated_at = datetime.utcnow()
            self.db.commit()
            return True
        return False
    
    def add_message(self, session_id: str, message_type: str, content: str, sources: List[str] = None, summary: str = None, suggestions: str = None) -> bool:
        """Add a message to the session"""
        # Update session timestamp
        session = self.get_session(session_id)
        if not session:
            return False

        session.updated_at = datetime.utcnow()

        # Add message
        db_message = ChatMessage(
            session_id=session_id,
            message_type=message_type,
            content=content,
            timestamp=datetime.utcnow(),
            sources=json.dumps(sources) if sources else None,
            summary=summary,
            suggestions=suggestions
        )
        self.db.add(db_message)
        self.db.commit()
        return True
    
    def get_messages(self, session_id: str) -> List[ChatMessage]:
        """Get all messages for a session"""
        return self.db.query(ChatMessage).filter(
            ChatMessage.session_id == session_id
        ).order_by(ChatMessage.timestamp.asc()).all()
    
    def generate_session_title(self, first_message: str) -> str:
        """Generate a title from the first user message"""
        # Simple title generation - take first 50 characters
        title = first_message.strip()
        if len(title) > 50:
            title = title[:47] + "..."
        return title if title else "New Chat"
