{"ast": null, "code": "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const {\n      children,\n      ...slotProps\n    } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map(child => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */jsx(SlotClone, {\n        ...slotProps,\n        ref: forwardedRef,\n        children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null\n      });\n    }\n    return /* @__PURE__ */jsx(SlotClone, {\n      ...slotProps,\n      ref: forwardedRef,\n      children\n    });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const {\n      children,\n      ...slotProps\n    } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({\n    children\n  }) => {\n    return /* @__PURE__ */jsx(Fragment2, {\n      children\n    });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = {\n    ...childProps\n  };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = {\n        ...slotPropValue,\n        ...childPropValue\n      };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return {\n    ...slotProps,\n    ...overrideProps\n  };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport { Slot as Root, Slot, Slottable, createSlot, createSlottable };", "map": {"version": 3, "names": ["React", "composeRefs", "Fragment", "Fragment2", "jsx", "createSlot", "ownerName", "SlotClone", "createSlotClone", "Slot2", "forwardRef", "props", "forwardedRef", "children", "slotProps", "childrenA<PERSON>y", "Children", "toArray", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "count", "only", "isValidElement", "ref", "cloneElement", "displayName", "Slot", "childrenRef", "getElementRef", "props2", "mergeProps", "type", "SLOTTABLE_IDENTIFIER", "Symbol", "createSlottable", "Slottable2", "Slottable", "__radixId", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "<PERSON><PERSON><PERSON><PERSON>", "test", "args", "result", "filter", "Boolean", "join", "element", "getter", "Object", "getOwnPropertyDescriptor", "get", "<PERSON><PERSON><PERSON><PERSON>", "isReactWarning"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-slot\\src\\slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AACvB,SAASC,WAAA,QAAmB;AAmCpB,SAkEGC,QAAA,IAAAC,SAAA,EAlEHC,GAAA;AAAA;AAzB0B,SAASC,WAAWC,SAAA,EAAmB;EACvE,MAAMC,SAAA,GAAY,eAAAC,eAAA,CAAgBF,SAAS;EAC3C,MAAMG,KAAA,GAAaT,KAAA,CAAAU,UAAA,CAAmC,CAACC,KAAA,EAAOC,YAAA,KAAiB;IAC7E,MAAM;MAAEC,QAAA;MAAU,GAAGC;IAAU,IAAIH,KAAA;IACnC,MAAMI,aAAA,GAAsBf,KAAA,CAAAgB,QAAA,CAASC,OAAA,CAAQJ,QAAQ;IACrD,MAAMK,SAAA,GAAYH,aAAA,CAAcI,IAAA,CAAKC,WAAW;IAEhD,IAAIF,SAAA,EAAW;MAEb,MAAMG,UAAA,GAAaH,SAAA,CAAUP,KAAA,CAAME,QAAA;MAEnC,MAAMS,WAAA,GAAcP,aAAA,CAAcQ,GAAA,CAAKC,KAAA,IAAU;QAC/C,IAAIA,KAAA,KAAUN,SAAA,EAAW;UAGvB,IAAUlB,KAAA,CAAAgB,QAAA,CAASS,KAAA,CAAMJ,UAAU,IAAI,GAAG,OAAarB,KAAA,CAAAgB,QAAA,CAASU,IAAA,CAAK,IAAI;UACzE,OAAa1B,KAAA,CAAA2B,cAAA,CAAeN,UAAU,IACjCA,UAAA,CAAWV,KAAA,CAAwCE,QAAA,GACpD;QACN,OAAO;UACL,OAAOW,KAAA;QACT;MACF,CAAC;MAED,OACE,eAAApB,GAAA,CAACG,SAAA;QAAW,GAAGO,SAAA;QAAWc,GAAA,EAAKhB,YAAA;QAC5BC,QAAA,EAAMb,KAAA,CAAA2B,cAAA,CAAeN,UAAU,IACtBrB,KAAA,CAAA6B,YAAA,CAAaR,UAAA,EAAY,QAAWC,WAAW,IACrD;MAAA,CACN;IAEJ;IAEA,OACE,eAAAlB,GAAA,CAACG,SAAA;MAAW,GAAGO,SAAA;MAAWc,GAAA,EAAKhB,YAAA;MAC5BC;IAAA,CACH;EAEJ,CAAC;EAEDJ,KAAA,CAAKqB,WAAA,GAAc,GAAGxB,SAAS;EAC/B,OAAOG,KAAA;AACT;AAEA,IAAMsB,IAAA,GAAO,eAAA1B,UAAA,CAAW,MAAM;AAAA;AAUH,SAASG,gBAAgBF,SAAA,EAAmB;EACrE,MAAMC,SAAA,GAAkBP,KAAA,CAAAU,UAAA,CAAgC,CAACC,KAAA,EAAOC,YAAA,KAAiB;IAC/E,MAAM;MAAEC,QAAA;MAAU,GAAGC;IAAU,IAAIH,KAAA;IAEnC,IAAUX,KAAA,CAAA2B,cAAA,CAAed,QAAQ,GAAG;MAClC,MAAMmB,WAAA,GAAcC,aAAA,CAAcpB,QAAQ;MAC1C,MAAMqB,MAAA,GAAQC,UAAA,CAAWrB,SAAA,EAAWD,QAAA,CAASF,KAAiB;MAE9D,IAAIE,QAAA,CAASuB,IAAA,KAAepC,KAAA,CAAAE,QAAA,EAAU;QACpCgC,MAAA,CAAMN,GAAA,GAAMhB,YAAA,GAAeX,WAAA,CAAYW,YAAA,EAAcoB,WAAW,IAAIA,WAAA;MACtE;MACA,OAAahC,KAAA,CAAA6B,YAAA,CAAahB,QAAA,EAAUqB,MAAK;IAC3C;IAEA,OAAalC,KAAA,CAAAgB,QAAA,CAASS,KAAA,CAAMZ,QAAQ,IAAI,IAAUb,KAAA,CAAAgB,QAAA,CAASU,IAAA,CAAK,IAAI,IAAI;EAC1E,CAAC;EAEDnB,SAAA,CAAUuB,WAAA,GAAc,GAAGxB,SAAS;EACpC,OAAOC,SAAA;AACT;AAMA,IAAM8B,oBAAA,GAAuBC,MAAA,CAAO,iBAAiB;AAAA;AAUnB,SAASC,gBAAgBjC,SAAA,EAAmB;EAC5E,MAAMkC,UAAA,GAAgCC,CAAC;IAAE5B;EAAS,MAAM;IACtD,OAAO,eAAAT,GAAA,CAAAD,SAAA;MAAGU;IAAA,CAAS;EACrB;EACA2B,UAAA,CAAUV,WAAA,GAAc,GAAGxB,SAAS;EACpCkC,UAAA,CAAUE,SAAA,GAAYL,oBAAA;EACtB,OAAOG,UAAA;AACT;AAEA,IAAMC,SAAA,GAAY,eAAAF,eAAA,CAAgB,WAAW;AAM7C,SAASnB,YACPI,KAAA,EAC+D;EAC/D,OACQxB,KAAA,CAAA2B,cAAA,CAAeH,KAAK,KAC1B,OAAOA,KAAA,CAAMY,IAAA,KAAS,cACtB,eAAeZ,KAAA,CAAMY,IAAA,IACrBZ,KAAA,CAAMY,IAAA,CAAKM,SAAA,KAAcL,oBAAA;AAE7B;AAEA,SAASF,WAAWrB,SAAA,EAAqB6B,UAAA,EAAsB;EAE7D,MAAMC,aAAA,GAAgB;IAAE,GAAGD;EAAW;EAEtC,WAAWE,QAAA,IAAYF,UAAA,EAAY;IACjC,MAAMG,aAAA,GAAgBhC,SAAA,CAAU+B,QAAQ;IACxC,MAAME,cAAA,GAAiBJ,UAAA,CAAWE,QAAQ;IAE1C,MAAMG,SAAA,GAAY,WAAWC,IAAA,CAAKJ,QAAQ;IAC1C,IAAIG,SAAA,EAAW;MAEb,IAAIF,aAAA,IAAiBC,cAAA,EAAgB;QACnCH,aAAA,CAAcC,QAAQ,IAAI,IAAIK,IAAA,KAAoB;UAChD,MAAMC,MAAA,GAASJ,cAAA,CAAe,GAAGG,IAAI;UACrCJ,aAAA,CAAc,GAAGI,IAAI;UACrB,OAAOC,MAAA;QACT;MACF,WAESL,aAAA,EAAe;QACtBF,aAAA,CAAcC,QAAQ,IAAIC,aAAA;MAC5B;IACF,WAESD,QAAA,KAAa,SAAS;MAC7BD,aAAA,CAAcC,QAAQ,IAAI;QAAE,GAAGC,aAAA;QAAe,GAAGC;MAAe;IAClE,WAAWF,QAAA,KAAa,aAAa;MACnCD,aAAA,CAAcC,QAAQ,IAAI,CAACC,aAAA,EAAeC,cAAc,EAAEK,MAAA,CAAOC,OAAO,EAAEC,IAAA,CAAK,GAAG;IACpF;EACF;EAEA,OAAO;IAAE,GAAGxC,SAAA;IAAW,GAAG8B;EAAc;AAC1C;AAOA,SAASX,cAAcsB,OAAA,EAA6B;EAElD,IAAIC,MAAA,GAASC,MAAA,CAAOC,wBAAA,CAAyBH,OAAA,CAAQ5C,KAAA,EAAO,KAAK,GAAGgD,GAAA;EACpE,IAAIC,OAAA,GAAUJ,MAAA,IAAU,oBAAoBA,MAAA,IAAUA,MAAA,CAAOK,cAAA;EAC7D,IAAID,OAAA,EAAS;IACX,OAAQL,OAAA,CAAgB3B,GAAA;EAC1B;EAGA4B,MAAA,GAASC,MAAA,CAAOC,wBAAA,CAAyBH,OAAA,EAAS,KAAK,GAAGI,GAAA;EAC1DC,OAAA,GAAUJ,MAAA,IAAU,oBAAoBA,MAAA,IAAUA,MAAA,CAAOK,cAAA;EACzD,IAAID,OAAA,EAAS;IACX,OAAQL,OAAA,CAAQ5C,KAAA,CAAuCiB,GAAA;EACzD;EAGA,OAAQ2B,OAAA,CAAQ5C,KAAA,CAAuCiB,GAAA,IAAQ2B,OAAA,CAAgB3B,GAAA;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}