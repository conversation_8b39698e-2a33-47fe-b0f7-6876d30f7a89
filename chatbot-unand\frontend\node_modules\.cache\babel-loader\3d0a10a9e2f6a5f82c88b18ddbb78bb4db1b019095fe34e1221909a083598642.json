{"ast": null, "code": "\"use client\";\n\n// src/collection-legacy.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n    collectionRef: {\n      current: null\n    },\n    itemMap: /* @__PURE__ */new Map()\n  });\n  const CollectionProvider = props => {\n    const {\n      scope,\n      children\n    } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */new Map()).current;\n    return /* @__PURE__ */jsx(CollectionProviderImpl, {\n      scope,\n      itemMap,\n      collectionRef: ref,\n      children\n    });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef((props, forwardedRef) => {\n    const {\n      scope,\n      children\n    } = props;\n    const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n    const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n    return /* @__PURE__ */jsx(CollectionSlotImpl, {\n      ref: composedRefs,\n      children\n    });\n  });\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef((props, forwardedRef) => {\n    const {\n      scope,\n      children,\n      ...itemData\n    } = props;\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n    React.useEffect(() => {\n      context.itemMap.set(ref, {\n        ref,\n        ...itemData\n      });\n      return () => void context.itemMap.delete(ref);\n    });\n    return /* @__PURE__ */jsx(CollectionItemSlotImpl, {\n      ...{\n        [ITEM_DATA_ATTR]: \"\"\n      },\n      ref: composedRefs,\n      children\n    });\n  });\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort((a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [{\n    Provider: CollectionProvider,\n    Slot: CollectionSlot,\n    ItemSlot: CollectionItemSlot\n  }, useCollection, createCollectionScope];\n}\n\n// src/collection.tsx\nimport React2 from \"react\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createSlot as createSlot2 } from \"@radix-ui/react-slot\";\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope2(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n    collectionElement: null,\n    collectionRef: {\n      current: null\n    },\n    collectionRefObject: {\n      current: null\n    },\n    itemMap: new OrderedDict(),\n    setItemMap: () => void 0\n  });\n  const CollectionProvider = ({\n    state,\n    ...props\n  }) => {\n    return state ? /* @__PURE__ */jsx2(CollectionProviderImpl, {\n      ...props,\n      state\n    }) : /* @__PURE__ */jsx2(CollectionInit, {\n      ...props\n    });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = props => {\n    const state = useInitCollection();\n    return /* @__PURE__ */jsx2(CollectionProviderImpl, {\n      ...props,\n      state\n    });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = props => {\n    const {\n      scope,\n      children,\n      state\n    } = props;\n    const ref = React2.useRef(null);\n    const [collectionElement, setCollectionElement] = React2.useState(null);\n    const composeRefs = useComposedRefs2(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    React2.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {});\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */jsx2(CollectionContextProvider, {\n      scope,\n      itemMap,\n      setItemMap,\n      collectionRef: composeRefs,\n      collectionRefObject: ref,\n      collectionElement,\n      children\n    });\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot2(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React2.forwardRef((props, forwardedRef) => {\n    const {\n      scope,\n      children\n    } = props;\n    const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n    const composedRefs = useComposedRefs2(forwardedRef, context.collectionRef);\n    return /* @__PURE__ */jsx2(CollectionSlotImpl, {\n      ref: composedRefs,\n      children\n    });\n  });\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot2(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React2.forwardRef((props, forwardedRef) => {\n    const {\n      scope,\n      children,\n      ...itemData\n    } = props;\n    const ref = React2.useRef(null);\n    const [element, setElement] = React2.useState(null);\n    const composedRefs = useComposedRefs2(forwardedRef, ref, setElement);\n    const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n    const {\n      setItemMap\n    } = context;\n    const itemDataRef = React2.useRef(itemData);\n    if (!shallowEqual(itemDataRef.current, itemData)) {\n      itemDataRef.current = itemData;\n    }\n    const memoizedItemData = itemDataRef.current;\n    React2.useEffect(() => {\n      const itemData2 = memoizedItemData;\n      setItemMap(map => {\n        if (!element) {\n          return map;\n        }\n        if (!map.has(element)) {\n          map.set(element, {\n            ...itemData2,\n            element\n          });\n          return map.toSorted(sortByDocumentPosition);\n        }\n        return map.set(element, {\n          ...itemData2,\n          element\n        }).toSorted(sortByDocumentPosition);\n      });\n      return () => {\n        setItemMap(map => {\n          if (!element || !map.has(element)) {\n            return map;\n          }\n          map.delete(element);\n          return new OrderedDict(map);\n        });\n      };\n    }, [element, memoizedItemData, setItemMap]);\n    return /* @__PURE__ */jsx2(CollectionItemSlotImpl, {\n      ...{\n        [ITEM_DATA_ATTR]: \"\"\n      },\n      ref: composedRefs,\n      children\n    });\n  });\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return React2.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const {\n      itemMap\n    } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [{\n    Provider: CollectionProvider,\n    Slot: CollectionSlot,\n    ItemSlot: CollectionItemSlot\n  }, functions];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver(mutationsList => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\nexport { createCollection, createCollection2 as unstable_createCollection };", "map": {"version": 3, "names": ["React", "createContextScope", "useComposedRefs", "createSlot", "jsx", "createCollection", "name", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "current", "itemMap", "Map", "CollectionProvider", "props", "scope", "children", "ref", "useRef", "displayName", "COLLECTION_SLOT_NAME", "CollectionSlotImpl", "CollectionSlot", "forwardRef", "forwardedRef", "context", "composedRefs", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlotImpl", "CollectionItemSlot", "itemData", "useEffect", "set", "delete", "useCollection", "getItems", "useCallback", "collectionNode", "orderedNodes", "Array", "from", "querySelectorAll", "items", "values", "orderedItems", "sort", "a", "b", "indexOf", "Provider", "Slot", "ItemSlot", "React2", "createContextScope2", "useComposedRefs2", "createSlot2", "__instanciated", "WeakMap", "OrderedDict", "_OrderedDict", "keys", "constructor", "entries", "key", "value", "get", "has", "push", "insert", "index", "length", "relativeIndex", "toSafeInteger", "actualIndex", "safeIndex", "size", "nextValue", "shouldSkip", "i", "<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "currentValue", "with", "copy", "before", "entryAt", "setBefore", "new<PERSON>ey", "after", "setAfter", "first", "last", "clear", "deleted", "splice", "deleteAt", "keyAt", "at", "offset", "dest", "keyFrom", "find", "predicate", "thisArg", "entry", "Reflect", "apply", "findIndex", "filter", "map", "callbackfn", "reduce", "args", "initialValue", "accumulator", "reduceRight", "toSorted", "compareFn", "toReversed", "reversed", "element", "toSpliced", "slice", "start", "end", "result", "stop", "every", "some", "array", "prototype", "call", "toSafeIndex", "number", "Math", "trunc", "jsx2", "createCollection2", "CollectionContextProvider", "collectionElement", "collectionRefObject", "setItemMap", "state", "CollectionInit", "useInitCollection", "setCollectionElement", "useState", "composeRefs", "observer", "getChildListObserver", "observe", "childList", "subtree", "disconnect", "setElement", "itemDataRef", "shallowEqual", "memoizedItemData", "itemData2", "sortByDocumentPosition", "functions", "keysA", "Object", "keysB", "hasOwnProperty", "isElementPreceding", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_PRECEDING", "callback", "MutationObserver", "mutationsList", "mutation", "type"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-collection\\src\\collection-legacy.tsx", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-collection\\src\\collection.tsx", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-collection\\src\\ordered-dictionary.ts"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAA,MAAW;AAClB,SAASC,kBAAA,QAA0B;AACnC,SAASC,eAAA,QAAuB;AAChC,SAASC,UAAA,QAA6B;AAuChC,SAAAC,GAAA;AA1BN,SAASC,iBAAiEC,IAAA,EAAc;EAKtF,MAAMC,aAAA,GAAgBD,IAAA,GAAO;EAC7B,MAAM,CAACE,uBAAA,EAAyBC,qBAAqB,IAAIR,kBAAA,CAAmBM,aAAa;EAUzF,MAAM,CAACG,sBAAA,EAAwBC,oBAAoB,IAAIH,uBAAA,CACrDD,aAAA,EACA;IAAEK,aAAA,EAAe;MAAEC,OAAA,EAAS;IAAK;IAAGC,OAAA,EAAS,mBAAIC,GAAA,CAAI;EAAE,CACzD;EAEA,MAAMC,kBAAA,GAA4EC,KAAA,IAAU;IAC1F,MAAM;MAAEC,KAAA;MAAOC;IAAS,IAAIF,KAAA;IAC5B,MAAMG,GAAA,GAAMpB,KAAA,CAAMqB,MAAA,CAA0B,IAAI;IAChD,MAAMP,OAAA,GAAUd,KAAA,CAAMqB,MAAA,CAAgC,mBAAIN,GAAA,CAAI,CAAC,EAAEF,OAAA;IACjE,OACE,eAAAT,GAAA,CAACM,sBAAA;MAAuBQ,KAAA;MAAcJ,OAAA;MAAkBF,aAAA,EAAeQ,GAAA;MACpED;IAAA,CACH;EAEJ;EAEAH,kBAAA,CAAmBM,WAAA,GAAcf,aAAA;EAMjC,MAAMgB,oBAAA,GAAuBjB,IAAA,GAAO;EAEpC,MAAMkB,kBAAA,GAAqBrB,UAAA,CAAWoB,oBAAoB;EAC1D,MAAME,cAAA,GAAiBzB,KAAA,CAAM0B,UAAA,CAC3B,CAACT,KAAA,EAAOU,YAAA,KAAiB;IACvB,MAAM;MAAET,KAAA;MAAOC;IAAS,IAAIF,KAAA;IAC5B,MAAMW,OAAA,GAAUjB,oBAAA,CAAqBY,oBAAA,EAAsBL,KAAK;IAChE,MAAMW,YAAA,GAAe3B,eAAA,CAAgByB,YAAA,EAAcC,OAAA,CAAQhB,aAAa;IACxE,OAAO,eAAAR,GAAA,CAACoB,kBAAA;MAAmBJ,GAAA,EAAKS,YAAA;MAAeV;IAAA,CAAS;EAC1D,CACF;EAEAM,cAAA,CAAeH,WAAA,GAAcC,oBAAA;EAM7B,MAAMO,cAAA,GAAiBxB,IAAA,GAAO;EAC9B,MAAMyB,cAAA,GAAiB;EAOvB,MAAMC,sBAAA,GAAyB7B,UAAA,CAAW2B,cAAc;EACxD,MAAMG,kBAAA,GAAqBjC,KAAA,CAAM0B,UAAA,CAC/B,CAACT,KAAA,EAAOU,YAAA,KAAiB;IACvB,MAAM;MAAET,KAAA;MAAOC,QAAA;MAAU,GAAGe;IAAS,IAAIjB,KAAA;IACzC,MAAMG,GAAA,GAAMpB,KAAA,CAAMqB,MAAA,CAAoB,IAAI;IAC1C,MAAMQ,YAAA,GAAe3B,eAAA,CAAgByB,YAAA,EAAcP,GAAG;IACtD,MAAMQ,OAAA,GAAUjB,oBAAA,CAAqBmB,cAAA,EAAgBZ,KAAK;IAE1DlB,KAAA,CAAMmC,SAAA,CAAU,MAAM;MACpBP,OAAA,CAAQd,OAAA,CAAQsB,GAAA,CAAIhB,GAAA,EAAK;QAAEA,GAAA;QAAK,GAAIc;MAAiC,CAAC;MACtE,OAAO,MAAM,KAAKN,OAAA,CAAQd,OAAA,CAAQuB,MAAA,CAAOjB,GAAG;IAC9C,CAAC;IAED,OACE,eAAAhB,GAAA,CAAC4B,sBAAA;MAAwB,GAAG;QAAE,CAACD,cAAc,GAAG;MAAG;MAAGX,GAAA,EAAKS,YAAA;MACxDV;IAAA,CACH;EAEJ,CACF;EAEAc,kBAAA,CAAmBX,WAAA,GAAcQ,cAAA;EAMjC,SAASQ,cAAcpB,KAAA,EAAY;IACjC,MAAMU,OAAA,GAAUjB,oBAAA,CAAqBL,IAAA,GAAO,sBAAsBY,KAAK;IAEvE,MAAMqB,QAAA,GAAWvC,KAAA,CAAMwC,WAAA,CAAY,MAAM;MACvC,MAAMC,cAAA,GAAiBb,OAAA,CAAQhB,aAAA,CAAcC,OAAA;MAC7C,IAAI,CAAC4B,cAAA,EAAgB,OAAO,EAAC;MAC7B,MAAMC,YAAA,GAAeC,KAAA,CAAMC,IAAA,CAAKH,cAAA,CAAeI,gBAAA,CAAiB,IAAId,cAAc,GAAG,CAAC;MACtF,MAAMe,KAAA,GAAQH,KAAA,CAAMC,IAAA,CAAKhB,OAAA,CAAQd,OAAA,CAAQiC,MAAA,CAAO,CAAC;MACjD,MAAMC,YAAA,GAAeF,KAAA,CAAMG,IAAA,CACzB,CAACC,CAAA,EAAGC,CAAA,KAAMT,YAAA,CAAaU,OAAA,CAAQF,CAAA,CAAE9B,GAAA,CAAIP,OAAQ,IAAI6B,YAAA,CAAaU,OAAA,CAAQD,CAAA,CAAE/B,GAAA,CAAIP,OAAQ,CACtF;MACA,OAAOmC,YAAA;IACT,GAAG,CAACpB,OAAA,CAAQhB,aAAA,EAAegB,OAAA,CAAQd,OAAO,CAAC;IAE3C,OAAOyB,QAAA;EACT;EAEA,OAAO,CACL;IAAEc,QAAA,EAAUrC,kBAAA;IAAoBsC,IAAA,EAAM7B,cAAA;IAAgB8B,QAAA,EAAUtB;EAAmB,GACnFK,aAAA,EACA7B,qBAAA,CACF;AACF;;;ACjIA,OAAO+C,MAAA,MAAW;AAClB,SAASvD,kBAAA,IAAAwD,mBAAA,QAA0B;AACnC,SAASvD,eAAA,IAAAwD,gBAAA,QAAuB;AAChC,SAASvD,UAAA,IAAAwD,WAAA,QAA6B;;;ACAtC,IAAMC,cAAA,GAAiB,mBAAIC,OAAA,CAAwC;AAC5D,IAAMC,WAAA,GAAN,MAAMC,YAAA,SAA0BhD,GAAA,CAAU;EAC/C,CAAAiD,IAAA;EAGAC,YAAYC,OAAA,EAA+C;IACzD,MAAMA,OAAO;IACb,KAAK,CAAAF,IAAA,GAAQ,CAAC,GAAG,MAAMA,IAAA,CAAK,CAAC;IAC7BJ,cAAA,CAAexB,GAAA,CAAI,MAAM,IAAI;EAC/B;EAEAA,IAAI+B,GAAA,EAAQC,KAAA,EAAU;IACpB,IAAIR,cAAA,CAAeS,GAAA,CAAI,IAAI,GAAG;MAC5B,IAAI,KAAKC,GAAA,CAAIH,GAAG,GAAG;QACjB,KAAK,CAAAH,IAAA,CAAM,KAAK,CAAAA,IAAA,CAAMZ,OAAA,CAAQe,GAAG,CAAC,IAAIA,GAAA;MACxC,OAAO;QACL,KAAK,CAAAH,IAAA,CAAMO,IAAA,CAAKJ,GAAG;MACrB;IACF;IACA,MAAM/B,GAAA,CAAI+B,GAAA,EAAKC,KAAK;IACpB,OAAO;EACT;EAEAI,OAAOC,KAAA,EAAeN,GAAA,EAAQC,KAAA,EAAU;IACtC,MAAME,GAAA,GAAM,KAAKA,GAAA,CAAIH,GAAG;IACxB,MAAMO,MAAA,GAAS,KAAK,CAAAV,IAAA,CAAMU,MAAA;IAC1B,MAAMC,aAAA,GAAgBC,aAAA,CAAcH,KAAK;IACzC,IAAII,WAAA,GAAcF,aAAA,IAAiB,IAAIA,aAAA,GAAgBD,MAAA,GAASC,aAAA;IAChE,MAAMG,SAAA,GAAYD,WAAA,GAAc,KAAKA,WAAA,IAAeH,MAAA,GAAS,KAAKG,WAAA;IAElE,IAAIC,SAAA,KAAc,KAAKC,IAAA,IAAST,GAAA,IAAOQ,SAAA,KAAc,KAAKC,IAAA,GAAO,KAAMD,SAAA,KAAc,IAAI;MACvF,KAAK1C,GAAA,CAAI+B,GAAA,EAAKC,KAAK;MACnB,OAAO;IACT;IAEA,MAAMW,IAAA,GAAO,KAAKA,IAAA,IAAQT,GAAA,GAAM,IAAI;IAMpC,IAAIK,aAAA,GAAgB,GAAG;MACrBE,WAAA;IACF;IAEA,MAAMb,IAAA,GAAO,CAAC,GAAG,KAAK,CAAAA,IAAK;IAC3B,IAAIgB,SAAA;IACJ,IAAIC,UAAA,GAAa;IACjB,SAASC,CAAA,GAAIL,WAAA,EAAaK,CAAA,GAAIH,IAAA,EAAMG,CAAA,IAAK;MACvC,IAAIL,WAAA,KAAgBK,CAAA,EAAG;QACrB,IAAIC,OAAA,GAAUnB,IAAA,CAAKkB,CAAC;QACpB,IAAIlB,IAAA,CAAKkB,CAAC,MAAMf,GAAA,EAAK;UACnBgB,OAAA,GAAUnB,IAAA,CAAKkB,CAAA,GAAI,CAAC;QACtB;QACA,IAAIZ,GAAA,EAAK;UAEP,KAAKjC,MAAA,CAAO8B,GAAG;QACjB;QACAa,SAAA,GAAY,KAAKX,GAAA,CAAIc,OAAO;QAC5B,KAAK/C,GAAA,CAAI+B,GAAA,EAAKC,KAAK;MACrB,OAAO;QACL,IAAI,CAACa,UAAA,IAAcjB,IAAA,CAAKkB,CAAA,GAAI,CAAC,MAAMf,GAAA,EAAK;UACtCc,UAAA,GAAa;QACf;QACA,MAAMG,UAAA,GAAapB,IAAA,CAAKiB,UAAA,GAAaC,CAAA,GAAIA,CAAA,GAAI,CAAC;QAC9C,MAAMG,YAAA,GAAeL,SAAA;QACrBA,SAAA,GAAY,KAAKX,GAAA,CAAIe,UAAU;QAC/B,KAAK/C,MAAA,CAAO+C,UAAU;QACtB,KAAKhD,GAAA,CAAIgD,UAAA,EAAYC,YAAY;MACnC;IACF;IACA,OAAO;EACT;EAEAC,KAAKb,KAAA,EAAeN,GAAA,EAAQC,KAAA,EAAU;IACpC,MAAMmB,IAAA,GAAO,IAAIxB,YAAA,CAAY,IAAI;IACjCwB,IAAA,CAAKf,MAAA,CAAOC,KAAA,EAAON,GAAA,EAAKC,KAAK;IAC7B,OAAOmB,IAAA;EACT;EAEAC,OAAOrB,GAAA,EAAQ;IACb,MAAMM,KAAA,GAAQ,KAAK,CAAAT,IAAA,CAAMZ,OAAA,CAAQe,GAAG,IAAI;IACxC,IAAIM,KAAA,GAAQ,GAAG;MACb,OAAO;IACT;IACA,OAAO,KAAKgB,OAAA,CAAQhB,KAAK;EAC3B;EAAA;AAAA;AAAA;EAKAiB,UAAUvB,GAAA,EAAQwB,MAAA,EAAWvB,KAAA,EAAU;IACrC,MAAMK,KAAA,GAAQ,KAAK,CAAAT,IAAA,CAAMZ,OAAA,CAAQe,GAAG;IACpC,IAAIM,KAAA,KAAU,IAAI;MAChB,OAAO;IACT;IACA,OAAO,KAAKD,MAAA,CAAOC,KAAA,EAAOkB,MAAA,EAAQvB,KAAK;EACzC;EAEAwB,MAAMzB,GAAA,EAAQ;IACZ,IAAIM,KAAA,GAAQ,KAAK,CAAAT,IAAA,CAAMZ,OAAA,CAAQe,GAAG;IAClCM,KAAA,GAAQA,KAAA,KAAU,MAAMA,KAAA,KAAU,KAAKM,IAAA,GAAO,IAAI,KAAKN,KAAA,GAAQ;IAC/D,IAAIA,KAAA,KAAU,IAAI;MAChB,OAAO;IACT;IACA,OAAO,KAAKgB,OAAA,CAAQhB,KAAK;EAC3B;EAAA;AAAA;AAAA;EAKAoB,SAAS1B,GAAA,EAAQwB,MAAA,EAAWvB,KAAA,EAAU;IACpC,MAAMK,KAAA,GAAQ,KAAK,CAAAT,IAAA,CAAMZ,OAAA,CAAQe,GAAG;IACpC,IAAIM,KAAA,KAAU,IAAI;MAChB,OAAO;IACT;IACA,OAAO,KAAKD,MAAA,CAAOC,KAAA,GAAQ,GAAGkB,MAAA,EAAQvB,KAAK;EAC7C;EAEA0B,MAAA,EAAQ;IACN,OAAO,KAAKL,OAAA,CAAQ,CAAC;EACvB;EAEAM,KAAA,EAAO;IACL,OAAO,KAAKN,OAAA,CAAQ,EAAE;EACxB;EAEAO,MAAA,EAAQ;IACN,KAAK,CAAAhC,IAAA,GAAQ,EAAC;IACd,OAAO,MAAMgC,KAAA,CAAM;EACrB;EAEA3D,OAAO8B,GAAA,EAAQ;IACb,MAAM8B,OAAA,GAAU,MAAM5D,MAAA,CAAO8B,GAAG;IAChC,IAAI8B,OAAA,EAAS;MACX,KAAK,CAAAjC,IAAA,CAAMkC,MAAA,CAAO,KAAK,CAAAlC,IAAA,CAAMZ,OAAA,CAAQe,GAAG,GAAG,CAAC;IAC9C;IACA,OAAO8B,OAAA;EACT;EAEAE,SAAS1B,KAAA,EAAe;IACtB,MAAMN,GAAA,GAAM,KAAKiC,KAAA,CAAM3B,KAAK;IAC5B,IAAIN,GAAA,KAAQ,QAAW;MACrB,OAAO,KAAK9B,MAAA,CAAO8B,GAAG;IACxB;IACA,OAAO;EACT;EAEAkC,GAAG5B,KAAA,EAAe;IAChB,MAAMN,GAAA,GAAMkC,EAAA,CAAG,KAAK,CAAArC,IAAA,EAAOS,KAAK;IAChC,IAAIN,GAAA,KAAQ,QAAW;MACrB,OAAO,KAAKE,GAAA,CAAIF,GAAG;IACrB;EACF;EAEAsB,QAAQhB,KAAA,EAAmC;IACzC,MAAMN,GAAA,GAAMkC,EAAA,CAAG,KAAK,CAAArC,IAAA,EAAOS,KAAK;IAChC,IAAIN,GAAA,KAAQ,QAAW;MACrB,OAAO,CAACA,GAAA,EAAK,KAAKE,GAAA,CAAIF,GAAG,CAAE;IAC7B;EACF;EAEAf,QAAQe,GAAA,EAAQ;IACd,OAAO,KAAK,CAAAH,IAAA,CAAMZ,OAAA,CAAQe,GAAG;EAC/B;EAEAiC,MAAM3B,KAAA,EAAe;IACnB,OAAO4B,EAAA,CAAG,KAAK,CAAArC,IAAA,EAAOS,KAAK;EAC7B;EAEA7B,KAAKuB,GAAA,EAAQmC,MAAA,EAAgB;IAC3B,MAAM7B,KAAA,GAAQ,KAAKrB,OAAA,CAAQe,GAAG;IAC9B,IAAIM,KAAA,KAAU,IAAI;MAChB,OAAO;IACT;IACA,IAAI8B,IAAA,GAAO9B,KAAA,GAAQ6B,MAAA;IACnB,IAAIC,IAAA,GAAO,GAAGA,IAAA,GAAO;IACrB,IAAIA,IAAA,IAAQ,KAAKxB,IAAA,EAAMwB,IAAA,GAAO,KAAKxB,IAAA,GAAO;IAC1C,OAAO,KAAKsB,EAAA,CAAGE,IAAI;EACrB;EAEAC,QAAQrC,GAAA,EAAQmC,MAAA,EAAgB;IAC9B,MAAM7B,KAAA,GAAQ,KAAKrB,OAAA,CAAQe,GAAG;IAC9B,IAAIM,KAAA,KAAU,IAAI;MAChB,OAAO;IACT;IACA,IAAI8B,IAAA,GAAO9B,KAAA,GAAQ6B,MAAA;IACnB,IAAIC,IAAA,GAAO,GAAGA,IAAA,GAAO;IACrB,IAAIA,IAAA,IAAQ,KAAKxB,IAAA,EAAMwB,IAAA,GAAO,KAAKxB,IAAA,GAAO;IAC1C,OAAO,KAAKqB,KAAA,CAAMG,IAAI;EACxB;EAEAE,KACEC,SAAA,EACAC,OAAA,EACA;IACA,IAAIlC,KAAA,GAAQ;IACZ,WAAWmC,KAAA,IAAS,MAAM;MACxB,IAAIC,OAAA,CAAQC,KAAA,CAAMJ,SAAA,EAAWC,OAAA,EAAS,CAACC,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC,GAAG;QAC3D,OAAOmC,KAAA;MACT;MACAnC,KAAA;IACF;IACA,OAAO;EACT;EAEAsC,UACEL,SAAA,EACAC,OAAA,EACA;IACA,IAAIlC,KAAA,GAAQ;IACZ,WAAWmC,KAAA,IAAS,MAAM;MACxB,IAAIC,OAAA,CAAQC,KAAA,CAAMJ,SAAA,EAAWC,OAAA,EAAS,CAACC,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC,GAAG;QAC3D,OAAOA,KAAA;MACT;MACAA,KAAA;IACF;IACA,OAAO;EACT;EAYAuC,OACEN,SAAA,EACAC,OAAA,EACA;IACA,MAAMzC,OAAA,GAAyB,EAAC;IAChC,IAAIO,KAAA,GAAQ;IACZ,WAAWmC,KAAA,IAAS,MAAM;MACxB,IAAIC,OAAA,CAAQC,KAAA,CAAMJ,SAAA,EAAWC,OAAA,EAAS,CAACC,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC,GAAG;QAC3DP,OAAA,CAAQK,IAAA,CAAKqC,KAAK;MACpB;MACAnC,KAAA;IACF;IACA,OAAO,IAAIV,YAAA,CAAYG,OAAO;EAChC;EAEA+C,IACEC,UAAA,EACAP,OAAA,EACmB;IACnB,MAAMzC,OAAA,GAAoB,EAAC;IAC3B,IAAIO,KAAA,GAAQ;IACZ,WAAWmC,KAAA,IAAS,MAAM;MACxB1C,OAAA,CAAQK,IAAA,CAAK,CAACqC,KAAA,CAAM,CAAC,GAAGC,OAAA,CAAQC,KAAA,CAAMI,UAAA,EAAYP,OAAA,EAAS,CAACC,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC,CAAC,CAAC;MACjFA,KAAA;IACF;IACA,OAAO,IAAIV,YAAA,CAAYG,OAAO;EAChC;EA6BAiD,OAAA,GACKC,IAAA,EASH;IACA,MAAM,CAACF,UAAA,EAAYG,YAAY,IAAID,IAAA;IACnC,IAAI3C,KAAA,GAAQ;IACZ,IAAI6C,WAAA,GAAcD,YAAA,IAAgB,KAAKhB,EAAA,CAAG,CAAC;IAC3C,WAAWO,KAAA,IAAS,MAAM;MACxB,IAAInC,KAAA,KAAU,KAAK2C,IAAA,CAAK1C,MAAA,KAAW,GAAG;QACpC4C,WAAA,GAAcV,KAAA;MAChB,OAAO;QACLU,WAAA,GAAcT,OAAA,CAAQC,KAAA,CAAMI,UAAA,EAAY,MAAM,CAACI,WAAA,EAAaV,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC;MACjF;MACAA,KAAA;IACF;IACA,OAAO6C,WAAA;EACT;EA6BAC,YAAA,GACKH,IAAA,EASH;IACA,MAAM,CAACF,UAAA,EAAYG,YAAY,IAAID,IAAA;IACnC,IAAIE,WAAA,GAAcD,YAAA,IAAgB,KAAKhB,EAAA,CAAG,EAAE;IAC5C,SAAS5B,KAAA,GAAQ,KAAKM,IAAA,GAAO,GAAGN,KAAA,IAAS,GAAGA,KAAA,IAAS;MACnD,MAAMmC,KAAA,GAAQ,KAAKP,EAAA,CAAG5B,KAAK;MAC3B,IAAIA,KAAA,KAAU,KAAKM,IAAA,GAAO,KAAKqC,IAAA,CAAK1C,MAAA,KAAW,GAAG;QAChD4C,WAAA,GAAcV,KAAA;MAChB,OAAO;QACLU,WAAA,GAAcT,OAAA,CAAQC,KAAA,CAAMI,UAAA,EAAY,MAAM,CAACI,WAAA,EAAaV,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC;MACjF;IACF;IACA,OAAO6C,WAAA;EACT;EAEAE,SAASC,SAAA,EAAiE;IACxE,MAAMvD,OAAA,GAAU,CAAC,GAAG,KAAKA,OAAA,CAAQ,CAAC,EAAEjB,IAAA,CAAKwE,SAAS;IAClD,OAAO,IAAI1D,YAAA,CAAYG,OAAO;EAChC;EAEAwD,WAAA,EAAgC;IAC9B,MAAMC,QAAA,GAAW,IAAI5D,YAAA,CAAkB;IACvC,SAASU,KAAA,GAAQ,KAAKM,IAAA,GAAO,GAAGN,KAAA,IAAS,GAAGA,KAAA,IAAS;MACnD,MAAMN,GAAA,GAAM,KAAKiC,KAAA,CAAM3B,KAAK;MAC5B,MAAMmD,OAAA,GAAU,KAAKvD,GAAA,CAAIF,GAAG;MAC5BwD,QAAA,CAASvF,GAAA,CAAI+B,GAAA,EAAKyD,OAAO;IAC3B;IACA,OAAOD,QAAA;EACT;EAKAE,UAAA,GAAaT,IAAA,EAAgE;IAC3E,MAAMlD,OAAA,GAAU,CAAC,GAAG,KAAKA,OAAA,CAAQ,CAAC;IAClCA,OAAA,CAAQgC,MAAA,CAAO,GAAGkB,IAAI;IACtB,OAAO,IAAIrD,YAAA,CAAYG,OAAO;EAChC;EAEA4D,MAAMC,KAAA,EAAgBC,GAAA,EAAc;IAClC,MAAMC,MAAA,GAAS,IAAIlE,YAAA,CAAkB;IACrC,IAAImE,IAAA,GAAO,KAAKnD,IAAA,GAAO;IAEvB,IAAIgD,KAAA,KAAU,QAAW;MACvB,OAAOE,MAAA;IACT;IAEA,IAAIF,KAAA,GAAQ,GAAG;MACbA,KAAA,GAAQA,KAAA,GAAQ,KAAKhD,IAAA;IACvB;IAEA,IAAIiD,GAAA,KAAQ,UAAaA,GAAA,GAAM,GAAG;MAChCE,IAAA,GAAOF,GAAA,GAAM;IACf;IAEA,SAASvD,KAAA,GAAQsD,KAAA,EAAOtD,KAAA,IAASyD,IAAA,EAAMzD,KAAA,IAAS;MAC9C,MAAMN,GAAA,GAAM,KAAKiC,KAAA,CAAM3B,KAAK;MAC5B,MAAMmD,OAAA,GAAU,KAAKvD,GAAA,CAAIF,GAAG;MAC5B8D,MAAA,CAAO7F,GAAA,CAAI+B,GAAA,EAAKyD,OAAO;IACzB;IACA,OAAOK,MAAA;EACT;EAEAE,MACEzB,SAAA,EACAC,OAAA,EACA;IACA,IAAIlC,KAAA,GAAQ;IACZ,WAAWmC,KAAA,IAAS,MAAM;MACxB,IAAI,CAACC,OAAA,CAAQC,KAAA,CAAMJ,SAAA,EAAWC,OAAA,EAAS,CAACC,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC,GAAG;QAC5D,OAAO;MACT;MACAA,KAAA;IACF;IACA,OAAO;EACT;EAEA2D,KACE1B,SAAA,EACAC,OAAA,EACA;IACA,IAAIlC,KAAA,GAAQ;IACZ,WAAWmC,KAAA,IAAS,MAAM;MACxB,IAAIC,OAAA,CAAQC,KAAA,CAAMJ,SAAA,EAAWC,OAAA,EAAS,CAACC,KAAA,EAAOnC,KAAA,EAAO,IAAI,CAAC,GAAG;QAC3D,OAAO;MACT;MACAA,KAAA;IACF;IACA,OAAO;EACT;AACF;AAUA,SAAS4B,GAAMgC,KAAA,EAAqB5D,KAAA,EAA8B;EAChE,IAAI,QAAQ9B,KAAA,CAAM2F,SAAA,EAAW;IAC3B,OAAO3F,KAAA,CAAM2F,SAAA,CAAUjC,EAAA,CAAGkC,IAAA,CAAKF,KAAA,EAAO5D,KAAK;EAC7C;EACA,MAAMI,WAAA,GAAc2D,WAAA,CAAYH,KAAA,EAAO5D,KAAK;EAC5C,OAAOI,WAAA,KAAgB,KAAK,SAAYwD,KAAA,CAAMxD,WAAW;AAC3D;AAEA,SAAS2D,YAAYH,KAAA,EAAuB5D,KAAA,EAAe;EACzD,MAAMC,MAAA,GAAS2D,KAAA,CAAM3D,MAAA;EACrB,MAAMC,aAAA,GAAgBC,aAAA,CAAcH,KAAK;EACzC,MAAMI,WAAA,GAAcF,aAAA,IAAiB,IAAIA,aAAA,GAAgBD,MAAA,GAASC,aAAA;EAClE,OAAOE,WAAA,GAAc,KAAKA,WAAA,IAAeH,MAAA,GAAS,KAAKG,WAAA;AACzD;AAEA,SAASD,cAAc6D,MAAA,EAAgB;EAErC,OAAOA,MAAA,KAAWA,MAAA,IAAUA,MAAA,KAAW,IAAI,IAAIC,IAAA,CAAKC,KAAA,CAAMF,MAAM;AAClE;;;AD7YM,SAAArI,GAAA,IAAAwI,IAAA;AAzCN,SAASC,kBAGPvI,IAAA,EAAc;EAKd,MAAMC,aAAA,GAAgBD,IAAA,GAAO;EAC7B,MAAM,CAACE,uBAAA,EAAyBC,qBAAqB,IAAIgD,mBAAA,CAAmBlD,aAAa;EAUzF,MAAM,CAACuI,yBAAA,EAA2BnI,oBAAoB,IAAIH,uBAAA,CACxDD,aAAA,EACA;IACEwI,iBAAA,EAAmB;IACnBnI,aAAA,EAAe;MAAEC,OAAA,EAAS;IAAK;IAC/BmI,mBAAA,EAAqB;MAAEnI,OAAA,EAAS;IAAK;IACrCC,OAAA,EAAS,IAAIgD,WAAA,CAAY;IACzBmF,UAAA,EAAYA,CAAA,KAAM;EACpB,CACF;EAOA,MAAMjI,kBAAA,GAIDA,CAAC;IAAEkI,KAAA;IAAO,GAAGjI;EAAM,MAAM;IAC5B,OAAOiI,KAAA,GACL,eAAAN,IAAA,CAAClI,sBAAA;MAAwB,GAAGO,KAAA;MAAOiI;IAAA,CAAc,IAEjD,eAAAN,IAAA,CAACO,cAAA;MAAgB,GAAGlI;IAAA,CAAO;EAE/B;EACAD,kBAAA,CAAmBM,WAAA,GAAcf,aAAA;EAEjC,MAAM4I,cAAA,GAGAlI,KAAA,IAAU;IACd,MAAMiI,KAAA,GAAQE,iBAAA,CAAkB;IAChC,OAAO,eAAAR,IAAA,CAAClI,sBAAA;MAAwB,GAAGO,KAAA;MAAOiI;IAAA,CAAc;EAC1D;EACAC,cAAA,CAAe7H,WAAA,GAAcf,aAAA,GAAgB;EAE7C,MAAMG,sBAAA,GAIAO,KAAA,IAAU;IACd,MAAM;MAAEC,KAAA;MAAOC,QAAA;MAAU+H;IAAM,IAAIjI,KAAA;IACnC,MAAMG,GAAA,GAAMoC,MAAA,CAAMnC,MAAA,CAA0B,IAAI;IAChD,MAAM,CAAC0H,iBAAA,EAAmBM,oBAAoB,IAAI7F,MAAA,CAAM8F,QAAA,CACtD,IACF;IACA,MAAMC,WAAA,GAAc7F,gBAAA,CAAgBtC,GAAA,EAAKiI,oBAAoB;IAC7D,MAAM,CAACvI,OAAA,EAASmI,UAAU,IAAIC,KAAA;IAE9B1F,MAAA,CAAMrB,SAAA,CAAU,MAAM;MACpB,IAAI,CAAC4G,iBAAA,EAAmB;MAExB,MAAMS,QAAA,GAAWC,oBAAA,CAAqB,MAAM,CAkB5C,CAAC;MACDD,QAAA,CAASE,OAAA,CAAQX,iBAAA,EAAmB;QAClCY,SAAA,EAAW;QACXC,OAAA,EAAS;MACX,CAAC;MACD,OAAO,MAAM;QACXJ,QAAA,CAASK,UAAA,CAAW;MACtB;IACF,GAAG,CAACd,iBAAiB,CAAC;IAEtB,OACE,eAAAH,IAAA,CAACE,yBAAA;MACC5H,KAAA;MACAJ,OAAA;MACAmI,UAAA;MACArI,aAAA,EAAe2I,WAAA;MACfP,mBAAA,EAAqB5H,GAAA;MACrB2H,iBAAA;MAEC5H;IAAA,CACH;EAEJ;EAEAT,sBAAA,CAAuBY,WAAA,GAAcf,aAAA,GAAgB;EAMrD,MAAMgB,oBAAA,GAAuBjB,IAAA,GAAO;EAEpC,MAAMkB,kBAAA,GAAqBmC,WAAA,CAAWpC,oBAAoB;EAC1D,MAAME,cAAA,GAAiB+B,MAAA,CAAM9B,UAAA,CAC3B,CAACT,KAAA,EAAOU,YAAA,KAAiB;IACvB,MAAM;MAAET,KAAA;MAAOC;IAAS,IAAIF,KAAA;IAC5B,MAAMW,OAAA,GAAUjB,oBAAA,CAAqBY,oBAAA,EAAsBL,KAAK;IAChE,MAAMW,YAAA,GAAe6B,gBAAA,CAAgB/B,YAAA,EAAcC,OAAA,CAAQhB,aAAa;IACxE,OAAO,eAAAgI,IAAA,CAACpH,kBAAA;MAAmBJ,GAAA,EAAKS,YAAA;MAAeV;IAAA,CAAS;EAC1D,CACF;EAEAM,cAAA,CAAeH,WAAA,GAAcC,oBAAA;EAM7B,MAAMO,cAAA,GAAiBxB,IAAA,GAAO;EAC9B,MAAMyB,cAAA,GAAiB;EAOvB,MAAMC,sBAAA,GAAyB2B,WAAA,CAAW7B,cAAc;EACxD,MAAMG,kBAAA,GAAqBuB,MAAA,CAAM9B,UAAA,CAC/B,CAACT,KAAA,EAAOU,YAAA,KAAiB;IACvB,MAAM;MAAET,KAAA;MAAOC,QAAA;MAAU,GAAGe;IAAS,IAAIjB,KAAA;IACzC,MAAMG,GAAA,GAAMoC,MAAA,CAAMnC,MAAA,CAAoB,IAAI;IAC1C,MAAM,CAACuG,OAAA,EAASkC,UAAU,IAAItG,MAAA,CAAM8F,QAAA,CAA6B,IAAI;IACrE,MAAMzH,YAAA,GAAe6B,gBAAA,CAAgB/B,YAAA,EAAcP,GAAA,EAAK0I,UAAU;IAClE,MAAMlI,OAAA,GAAUjB,oBAAA,CAAqBmB,cAAA,EAAgBZ,KAAK;IAE1D,MAAM;MAAE+H;IAAW,IAAIrH,OAAA;IAEvB,MAAMmI,WAAA,GAAcvG,MAAA,CAAMnC,MAAA,CAAOa,QAAQ;IACzC,IAAI,CAAC8H,YAAA,CAAaD,WAAA,CAAYlJ,OAAA,EAASqB,QAAQ,GAAG;MAChD6H,WAAA,CAAYlJ,OAAA,GAAUqB,QAAA;IACxB;IACA,MAAM+H,gBAAA,GAAmBF,WAAA,CAAYlJ,OAAA;IAErC2C,MAAA,CAAMrB,SAAA,CAAU,MAAM;MACpB,MAAM+H,SAAA,GAAWD,gBAAA;MACjBhB,UAAA,CAAYhC,GAAA,IAAQ;QAClB,IAAI,CAACW,OAAA,EAAS;UACZ,OAAOX,GAAA;QACT;QAEA,IAAI,CAACA,GAAA,CAAI3C,GAAA,CAAIsD,OAAO,GAAG;UACrBX,GAAA,CAAI7E,GAAA,CAAIwF,OAAA,EAAS;YAAE,GAAIsC,SAAA;YAAkCtC;UAAQ,CAAC;UAClE,OAAOX,GAAA,CAAIO,QAAA,CAAS2C,sBAAsB;QAC5C;QAEA,OAAOlD,GAAA,CACJ7E,GAAA,CAAIwF,OAAA,EAAS;UAAE,GAAIsC,SAAA;UAAkCtC;QAAQ,CAAC,EAC9DJ,QAAA,CAAS2C,sBAAsB;MACpC,CAAC;MAED,OAAO,MAAM;QACXlB,UAAA,CAAYhC,GAAA,IAAQ;UAClB,IAAI,CAACW,OAAA,IAAW,CAACX,GAAA,CAAI3C,GAAA,CAAIsD,OAAO,GAAG;YACjC,OAAOX,GAAA;UACT;UACAA,GAAA,CAAI5E,MAAA,CAAOuF,OAAO;UAClB,OAAO,IAAI9D,WAAA,CAAYmD,GAAG;QAC5B,CAAC;MACH;IACF,GAAG,CAACW,OAAA,EAASqC,gBAAA,EAAkBhB,UAAU,CAAC;IAE1C,OACE,eAAAL,IAAA,CAAC5G,sBAAA;MAAwB,GAAG;QAAE,CAACD,cAAc,GAAG;MAAG;MAAGX,GAAA,EAAKS,YAAA;MACxDV;IAAA,CACH;EAEJ,CACF;EAEAc,kBAAA,CAAmBX,WAAA,GAAcQ,cAAA;EAMjC,SAASsH,kBAAA,EAAoB;IAC3B,OAAO5F,MAAA,CAAM8F,QAAA,CAAyC,IAAIxF,WAAA,CAAY,CAAC;EACzE;EAMA,SAASxB,cAAcpB,KAAA,EAAY;IACjC,MAAM;MAAEJ;IAAQ,IAAIH,oBAAA,CAAqBL,IAAA,GAAO,sBAAsBY,KAAK;IAE3E,OAAOJ,OAAA;EACT;EAEA,MAAMsJ,SAAA,GAAY;IAChB3J,qBAAA;IACA6B,aAAA;IACA8G;EACF;EAEA,OAAO,CACL;IAAE/F,QAAA,EAAUrC,kBAAA;IAAoBsC,IAAA,EAAM7B,cAAA;IAAgB8B,QAAA,EAAUtB;EAAmB,GACnFmI,SAAA,CACF;AACF;AAKA,SAASJ,aAAa9G,CAAA,EAAQC,CAAA,EAAQ;EACpC,IAAID,CAAA,KAAMC,CAAA,EAAG,OAAO;EACpB,IAAI,OAAOD,CAAA,KAAM,YAAY,OAAOC,CAAA,KAAM,UAAU,OAAO;EAC3D,IAAID,CAAA,IAAK,QAAQC,CAAA,IAAK,MAAM,OAAO;EACnC,MAAMkH,KAAA,GAAQC,MAAA,CAAOtG,IAAA,CAAKd,CAAC;EAC3B,MAAMqH,KAAA,GAAQD,MAAA,CAAOtG,IAAA,CAAKb,CAAC;EAC3B,IAAIkH,KAAA,CAAM3F,MAAA,KAAW6F,KAAA,CAAM7F,MAAA,EAAQ,OAAO;EAC1C,WAAWP,GAAA,IAAOkG,KAAA,EAAO;IACvB,IAAI,CAACC,MAAA,CAAOhC,SAAA,CAAUkC,cAAA,CAAejC,IAAA,CAAKpF,CAAA,EAAGgB,GAAG,GAAG,OAAO;IAC1D,IAAIjB,CAAA,CAAEiB,GAAG,MAAMhB,CAAA,CAAEgB,GAAG,GAAG,OAAO;EAChC;EACA,OAAO;AACT;AAEA,SAASsG,mBAAmBvH,CAAA,EAAYC,CAAA,EAAY;EAClD,OAAO,CAAC,EAAEA,CAAA,CAAEuH,uBAAA,CAAwBxH,CAAC,IAAIyH,IAAA,CAAKC,2BAAA;AAChD;AAEA,SAAST,uBACPjH,CAAA,EACAC,CAAA,EACA;EACA,OAAO,CAACD,CAAA,CAAE,CAAC,EAAE0E,OAAA,IAAW,CAACzE,CAAA,CAAE,CAAC,EAAEyE,OAAA,GAC1B,IACA6C,kBAAA,CAAmBvH,CAAA,CAAE,CAAC,EAAE0E,OAAA,EAASzE,CAAA,CAAE,CAAC,EAAEyE,OAAO,IAC3C,KACA;AACR;AAEA,SAAS6B,qBAAqBoB,QAAA,EAAsB;EAClD,MAAMrB,QAAA,GAAW,IAAIsB,gBAAA,CAAkBC,aAAA,IAAkB;IACvD,WAAWC,QAAA,IAAYD,aAAA,EAAe;MACpC,IAAIC,QAAA,CAASC,IAAA,KAAS,aAAa;QACjCJ,QAAA,CAAS;QACT;MACF;IACF;EACF,CAAC;EAED,OAAOrB,QAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}