[{"C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\api.js": "2", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\App.jsx": "4", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatWindow.jsx": "5", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatInput.jsx": "6", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatSidebar.jsx": "7", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\Message.jsx": "8", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\TelegramButton.jsx": "9", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ThemeToggle.jsx": "10", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\contexts\\ThemeContext.jsx": "11", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\switch.jsx": "12", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\lib\\utils.js": "13"}, {"size": 535, "mtime": 1748977841189, "results": "14", "hashOfConfig": "15"}, {"size": 3630, "mtime": 1748894611711, "results": "16", "hashOfConfig": "15"}, {"size": 377, "mtime": 1748845124954, "results": "17", "hashOfConfig": "15"}, {"size": 6482, "mtime": 1749026120364, "results": "18", "hashOfConfig": "15"}, {"size": 4902, "mtime": 1749026713875, "results": "19", "hashOfConfig": "15"}, {"size": 4676, "mtime": 1749026860171, "results": "20", "hashOfConfig": "15"}, {"size": 12902, "mtime": 1749026395998, "results": "21", "hashOfConfig": "15"}, {"size": 2922, "mtime": 1749026903887, "results": "22", "hashOfConfig": "15"}, {"size": 5951, "mtime": 1748977180572, "results": "23", "hashOfConfig": "15"}, {"size": 771, "mtime": 1749025822421, "results": "24", "hashOfConfig": "15"}, {"size": 1342, "mtime": 1749025809447, "results": "25", "hashOfConfig": "15"}, {"size": 1057, "mtime": 1749025705613, "results": "26", "hashOfConfig": "15"}, {"size": 135, "mtime": 1749025582622, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17di5rj", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\api.js", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatWindow.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatInput.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatSidebar.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\Message.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\TelegramButton.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ThemeToggle.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\contexts\\ThemeContext.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\switch.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\lib\\utils.js", [], []]