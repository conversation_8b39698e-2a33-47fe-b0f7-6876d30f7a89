import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv(dotenv_path=os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))

DATABASE_URL = os.getenv("DATABASE_URL")

def add_summary_column():
    """Add summary column to chat_messages table if it doesn't exist"""
    try:
        # Parse DATABASE_URL
        # Format: postgresql://user:password@host:port/database
        url_parts = DATABASE_URL.replace('postgresql://', '').split('@')
        user_pass = url_parts[0].split(':')
        host_port_db = url_parts[1].split('/')
        host_port = host_port_db[0].split(':')

        user = user_pass[0]
        password = user_pass[1]
        host = host_port[0]
        port = host_port[1]
        database = host_port_db[1]

        # Connect to database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password
        )

        cursor = conn.cursor()

        # Check if summary column exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='chat_messages' AND column_name='summary';
        """)

        result = cursor.fetchone()

        if not result:
            print("Adding summary column to chat_messages table...")
            cursor.execute("""
                ALTER TABLE chat_messages
                ADD COLUMN summary TEXT;
            """)
            conn.commit()
            print("Summary column added successfully!")
        else:
            print("Summary column already exists.")

        # Check if suggestions column exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='chat_messages' AND column_name='suggestions';
        """)

        result = cursor.fetchone()

        if not result:
            print("Adding suggestions column to chat_messages table...")
            cursor.execute("""
                ALTER TABLE chat_messages
                ADD COLUMN suggestions TEXT;
            """)
            conn.commit()
            print("Suggestions column added successfully!")
        else:
            print("Suggestions column already exists.")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error adding columns: {e}")
        return False

    return True

if __name__ == "__main__":
    add_summary_column()
