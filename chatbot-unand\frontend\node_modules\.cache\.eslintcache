[{"C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\api.js": "2", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\App.jsx": "4", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatWindow.jsx": "5", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatInput.jsx": "6", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatSidebar.jsx": "7", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\Message.jsx": "8", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\TelegramButton.jsx": "9", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ThemeToggle.jsx": "10", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\contexts\\ThemeContext.jsx": "11", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\switch.jsx": "12", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\lib\\utils.js": "13", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\accordion.jsx": "14"}, {"size": 535, "mtime": 1748977841189, "results": "15", "hashOfConfig": "16"}, {"size": 3630, "mtime": 1748894611711, "results": "17", "hashOfConfig": "16"}, {"size": 377, "mtime": 1748845124954, "results": "18", "hashOfConfig": "16"}, {"size": 6900, "mtime": 1749031924903, "results": "19", "hashOfConfig": "16"}, {"size": 4980, "mtime": 1749031889038, "results": "20", "hashOfConfig": "16"}, {"size": 4676, "mtime": 1749026860171, "results": "21", "hashOfConfig": "16"}, {"size": 12902, "mtime": 1749026395998, "results": "22", "hashOfConfig": "16"}, {"size": 8139, "mtime": 1749035090858, "results": "23", "hashOfConfig": "16"}, {"size": 5985, "mtime": 1749026945369, "results": "24", "hashOfConfig": "16"}, {"size": 771, "mtime": 1749025822421, "results": "25", "hashOfConfig": "16"}, {"size": 1342, "mtime": 1749025809447, "results": "26", "hashOfConfig": "16"}, {"size": 1057, "mtime": 1749025705613, "results": "27", "hashOfConfig": "16"}, {"size": 135, "mtime": 1749025582622, "results": "28", "hashOfConfig": "16"}, {"size": 1724, "mtime": 1749027809588, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17di5rj", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\api.js", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatWindow.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatInput.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\ChatSidebar.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\Message.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\TelegramButton.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ThemeToggle.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\contexts\\ThemeContext.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\switch.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\accordion.jsx", [], []]