{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\ui\\\\accordion.jsx\";\nimport * as React from \"react\";\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\";\nimport { ChevronDown } from \"lucide-react\";\nimport { cn } from \"../../lib/utils\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Accordion = AccordionPrimitive.Root;\nconst AccordionItem = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(AccordionPrimitive.Item, {\n  ref: ref,\n  className: cn(\"border-b\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this));\n_c2 = AccordionItem;\nAccordionItem.displayName = \"AccordionItem\";\nconst AccordionTrigger = /*#__PURE__*/React.forwardRef(_c3 = ({\n  className,\n  children,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(AccordionPrimitive.Header, {\n  className: \"flex\",\n  children: /*#__PURE__*/_jsxDEV(AccordionPrimitive.Trigger, {\n    ref: ref,\n    className: cn(\"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\", className),\n    ...props,\n    children: [children, /*#__PURE__*/_jsxDEV(ChevronDown, {\n      className: \"h-4 w-4 shrink-0 transition-transform duration-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 20,\n  columnNumber: 5\n}, this));\n_c4 = AccordionTrigger;\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;\nconst AccordionContent = /*#__PURE__*/React.forwardRef(_c5 = ({\n  className,\n  children,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(AccordionPrimitive.Content, {\n  ref: ref,\n  className: \"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\",\n  ...props,\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"pb-4 pt-0\", className),\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 39,\n  columnNumber: 5\n}, this));\n_c6 = AccordionContent;\nAccordionContent.displayName = AccordionPrimitive.Content.displayName;\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent };\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"AccordionItem$React.forwardRef\");\n$RefreshReg$(_c2, \"AccordionItem\");\n$RefreshReg$(_c3, \"AccordionTrigger$React.forwardRef\");\n$RefreshReg$(_c4, \"AccordionTrigger\");\n$RefreshReg$(_c5, \"AccordionContent$React.forwardRef\");\n$RefreshReg$(_c6, \"AccordionContent\");", "map": {"version": 3, "names": ["React", "AccordionPrimitive", "ChevronDown", "cn", "jsxDEV", "_jsxDEV", "Accordion", "Root", "AccordionItem", "forwardRef", "_c", "className", "props", "ref", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "AccordionTrigger", "_c3", "children", "Header", "<PERSON><PERSON>", "_c4", "Accordi<PERSON><PERSON><PERSON><PERSON>", "_c5", "Content", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/ui/accordion.jsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\";\r\nimport { ChevronDown } from \"lucide-react\";\r\n\r\nimport { cn } from \"../../lib/utils\";\r\n\r\nconst Accordion = AccordionPrimitive.Root;\r\n\r\nconst AccordionItem = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AccordionPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\"border-b\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAccordionItem.displayName = \"AccordionItem\";\r\n\r\nconst AccordionTrigger = React.forwardRef(\r\n  ({ className, children, ...props }, ref) => (\r\n    <AccordionPrimitive.Header className=\"flex\">\r\n      <AccordionPrimitive.Trigger\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\r\n      </AccordionPrimitive.Trigger>\r\n    </AccordionPrimitive.Header>\r\n  )\r\n);\r\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;\r\n\r\nconst AccordionContent = React.forwardRef(\r\n  ({ className, children, ...props }, ref) => (\r\n    <AccordionPrimitive.Content\r\n      ref={ref}\r\n      className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\r\n      {...props}\r\n    >\r\n      <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\r\n    </AccordionPrimitive.Content>\r\n  )\r\n);\r\n\r\nAccordionContent.displayName = AccordionPrimitive.Content.displayName;\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent };\r\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,kBAAkB,MAAM,2BAA2B;AAC/D,SAASC,WAAW,QAAQ,cAAc;AAE1C,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,SAAS,GAAGL,kBAAkB,CAACM,IAAI;AAEzC,MAAMC,aAAa,gBAAGR,KAAK,CAACS,UAAU,CAAAC,EAAA,GAACA,CAAC;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAClER,OAAA,CAACJ,kBAAkB,CAACa,IAAI;EACtBD,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAER,EAAE,CAAC,UAAU,EAAEQ,SAAS,CAAE;EAAA,GACjCC;AAAK;EAAAG,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACF,CAAC;AAACC,GAAA,GANGX,aAAa;AAOnBA,aAAa,CAACY,WAAW,GAAG,eAAe;AAE3C,MAAMC,gBAAgB,gBAAGrB,KAAK,CAACS,UAAU,CAAAa,GAAA,GACvCA,CAAC;EAAEX,SAAS;EAAEY,QAAQ;EAAE,GAAGX;AAAM,CAAC,EAAEC,GAAG,kBACrCR,OAAA,CAACJ,kBAAkB,CAACuB,MAAM;EAACb,SAAS,EAAC,MAAM;EAAAY,QAAA,eACzClB,OAAA,CAACJ,kBAAkB,CAACwB,OAAO;IACzBZ,GAAG,EAAEA,GAAI;IACTF,SAAS,EAAER,EAAE,CACX,8HAA8H,EAC9HQ,SACF,CAAE;IAAA,GACEC,KAAK;IAAAW,QAAA,GAERA,QAAQ,eACTlB,OAAA,CAACH,WAAW;MAACS,SAAS,EAAC;IAAoD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACJ,CAE/B,CAAC;AAACQ,GAAA,GAhBIL,gBAAgB;AAiBtBA,gBAAgB,CAACD,WAAW,GAAGnB,kBAAkB,CAACwB,OAAO,CAACL,WAAW;AAErE,MAAMO,gBAAgB,gBAAG3B,KAAK,CAACS,UAAU,CAAAmB,GAAA,GACvCA,CAAC;EAAEjB,SAAS;EAAEY,QAAQ;EAAE,GAAGX;AAAM,CAAC,EAAEC,GAAG,kBACrCR,OAAA,CAACJ,kBAAkB,CAAC4B,OAAO;EACzBhB,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAC,0HAA0H;EAAA,GAChIC,KAAK;EAAAW,QAAA,eAETlB,OAAA;IAAKM,SAAS,EAAER,EAAE,CAAC,WAAW,EAAEQ,SAAS,CAAE;IAAAY,QAAA,EAAEA;EAAQ;IAAAR,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClC,CAEhC,CAAC;AAACY,GAAA,GAVIH,gBAAgB;AAYtBA,gBAAgB,CAACP,WAAW,GAAGnB,kBAAkB,CAAC4B,OAAO,CAACT,WAAW;AAErE,SAASd,SAAS,EAAEE,aAAa,EAAEa,gBAAgB,EAAEM,gBAAgB;AAAG,IAAAjB,EAAA,EAAAS,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAArB,EAAA;AAAAqB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}