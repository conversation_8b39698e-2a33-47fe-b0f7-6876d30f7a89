import React from "react";

const Message = ({ message }) => {
  const { text, isBot, timestamp, sources, isError } = message;

  const formatTime = (date) => {
    return date.toLocaleTimeString("id-ID", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Function to format text with basic markdown-like formatting
  const formatText = (text) => {
    if (!text) return text;

    // Escape HTML first to prevent XSS
    let formatted = text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;");

    // Convert **bold** to <strong>
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");

    // Convert *italic* to <em>
    formatted = formatted.replace(/\*(.*?)\*/g, "<em>$1</em>");

    // Convert line breaks to <br>
    formatted = formatted.replace(/\n/g, "<br>");

    // Convert numbered lists (1. 2. 3.)
    formatted = formatted.replace(/^(\d+\.\s)/gm, "<strong>$1</strong>");

    // Convert bullet points (- or *)
    formatted = formatted.replace(/^[-*]\s/gm, "• ");

    // Convert ## headers to bold
    formatted = formatted.replace(/^##\s(.+)$/gm, "<strong>$1</strong>");

    return formatted;
  };

  return (
    <div className={`flex ${isBot ? "justify-start" : "justify-end"}`}>
      <div
        className={`max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${
          isBot
            ? isError
              ? "bg-red-50 dark:bg-red-900 text-red-800 dark:text-red-200 border-2 border-red-300 dark:border-red-600"
              : "bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-700 dark:to-gray-600 text-green-800 dark:text-green-200 border-2 border-green-300 dark:border-gray-500"
            : "bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white shadow-lg"
        }`}
      >
        <div
          className="text-sm sm:text-base leading-relaxed whitespace-pre-wrap"
          dangerouslySetInnerHTML={{ __html: formatText(text) }}
        />

        {sources && sources.length > 0 && (
          <div className="mt-3 pt-3 border-t border-green-400 dark:border-gray-500">
            <p className="text-xs font-semibold mb-2 text-green-700 dark:text-green-300">
              📚 Sumber Referensi:
            </p>
            <ul className="text-xs space-y-1 text-green-600 dark:text-green-400">
              {sources.map((source, index) => (
                <li key={index} className="truncate">
                  • {source}
                </li>
              ))}
            </ul>
          </div>
        )}

        <p
          className={`text-xs mt-2 ${
            isBot
              ? "text-green-600 dark:text-green-400"
              : "text-green-100 dark:text-green-200"
          }`}
        >
          {formatTime(timestamp)}
        </p>
      </div>
    </div>
  );
};

export default Message;
