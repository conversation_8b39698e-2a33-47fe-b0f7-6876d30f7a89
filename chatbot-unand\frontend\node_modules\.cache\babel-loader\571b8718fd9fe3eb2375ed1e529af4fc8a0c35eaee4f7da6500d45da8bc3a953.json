{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\Message.jsx\";\nimport React from \"react\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"./components/ui/accordion\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Message = ({\n  message\n}) => {\n  const {\n    text,\n    isBot,\n    timestamp,\n    sources,\n    isError,\n    summary,\n    suggestions,\n    sources_count,\n    is_greeting\n  } = message;\n  const formatTime = date => {\n    return date.toLocaleTimeString(\"id-ID\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n\n  // Function to format text with basic markdown-like formatting\n  const formatText = text => {\n    if (!text) return text;\n\n    // Escape HTML first to prevent XSS\n    let formatted = text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n\n    // Convert **bold** to <strong>\n    formatted = formatted.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\");\n\n    // Convert *italic* to <em>\n    formatted = formatted.replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n\n    // Convert line breaks to <br>\n    formatted = formatted.replace(/\\n/g, \"<br>\");\n\n    // Convert numbered lists (1. 2. 3.)\n    formatted = formatted.replace(/^(\\d+\\.\\s)/gm, \"<strong>$1</strong>\");\n\n    // Convert bullet points (- or *)\n    formatted = formatted.replace(/^[-*]\\s/gm, \"• \");\n\n    // Convert ## headers to bold\n    formatted = formatted.replace(/^##\\s(.+)$/gm, \"<strong>$1</strong>\");\n    return formatted;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isBot ? \"justify-start\" : \"justify-end\"}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${isBot ? isError ? \"bg-red-50 dark:bg-red-900 text-red-800 dark:text-red-200 border-2 border-red-300 dark:border-red-600\" : \"bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-700 dark:to-gray-600 text-green-800 dark:text-green-200 border-2 border-green-300 dark:border-gray-500\" : \"bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white shadow-lg\"}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm sm:text-base leading-relaxed whitespace-pre-wrap\",\n        dangerouslySetInnerHTML: {\n          __html: formatText(text)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), summary && isBot && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-3 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-bold text-green-800 dark:text-green-200 mb-2 flex items-center\",\n          children: \"\\uD83D\\uDCDD Kesimpulan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-green-700 dark:text-green-300 leading-relaxed\",\n          dangerouslySetInnerHTML: {\n            __html: formatText(summary)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), suggestions && isBot && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 p-3 bg-gradient-to-r from-blue-50 to-green-50 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-blue-300 dark:border-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-bold text-blue-800 dark:text-blue-200 mb-2 flex items-center\",\n          children: \"\\uD83D\\uDCA1 Saran\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-blue-700 dark:text-blue-300 leading-relaxed\",\n          dangerouslySetInnerHTML: {\n            __html: formatText(suggestions)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this), isBot && !is_greeting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 pt-3 border-t border-green-400 dark:border-gray-500\",\n        children: sources && sources.length > 0 ? /*#__PURE__*/_jsxDEV(Accordion, {\n          type: \"single\",\n          collapsible: true,\n          className: \"w-full\",\n          children: /*#__PURE__*/_jsxDEV(AccordionItem, {\n            value: \"sources\",\n            className: \"border-none\",\n            children: [/*#__PURE__*/_jsxDEV(AccordionTrigger, {\n              className: \"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [\"\\uD83D\\uDCDA Sumber Referensi (\", sources.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionContent, {\n              className: \"pt-0 pb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: sources.map((source, index) => {\n                  // Check if this is a document-specific answer with filename\n                  const filenameMatch = source.match(/^(.+\\.docx): (.+)$/s);\n                  if (filenameMatch) {\n                    const filename = filenameMatch[1];\n                    const docContent = filenameMatch[2];\n\n                    // Skip if no relevant information\n                    if (docContent.includes(\"Tidak ada informasi relevan\")) {\n                      return null;\n                    }\n                    return /*#__PURE__*/_jsxDEV(Accordion, {\n                      type: \"single\",\n                      collapsible: true,\n                      className: \"w-full\",\n                      children: /*#__PURE__*/_jsxDEV(AccordionItem, {\n                        value: `doc-${index}`,\n                        className: \"border border-green-200 dark:border-gray-600 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(AccordionTrigger, {\n                          className: \"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2 px-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\"\\uD83D\\uDCC4 \", filename]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 144,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 143,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(AccordionContent, {\n                          className: \"pt-0 pb-2 px-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"p-4 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-sm text-green-700 dark:text-green-300 leading-relaxed\",\n                              dangerouslySetInnerHTML: {\n                                __html: formatText(docContent)\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 150,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 149,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 148,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 139,\n                        columnNumber: 33\n                      }, this)\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 31\n                    }, this);\n                  }\n\n                  // Check if this is a document-specific answer (starting with \"Dokumen X:\")\n                  const isDocumentAnswer = source.startsWith(\"Dokumen \");\n                  if (isDocumentAnswer) {\n                    // Extract document number and content\n                    const match = source.match(/^Dokumen (\\d+): (.+)$/s);\n                    if (match) {\n                      const docNumber = match[1];\n                      const docContent = match[2];\n\n                      // Skip if no relevant information\n                      if (docContent.includes(\"Tidak ada informasi relevan\")) {\n                        return null;\n                      }\n                      return /*#__PURE__*/_jsxDEV(Accordion, {\n                        type: \"single\",\n                        collapsible: true,\n                        className: \"w-full\",\n                        children: /*#__PURE__*/_jsxDEV(AccordionItem, {\n                          value: `doc-${docNumber}`,\n                          className: \"border border-green-200 dark:border-gray-600 rounded-lg\",\n                          children: [/*#__PURE__*/_jsxDEV(AccordionTrigger, {\n                            className: \"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2 px-3\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"flex items-center gap-2\",\n                              children: [\"\\uD83D\\uDCC4 Dokumen \", docNumber]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 197,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 196,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(AccordionContent, {\n                            className: \"pt-0 pb-2 px-3\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"p-4 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-sm text-green-700 dark:text-green-300 leading-relaxed\",\n                                dangerouslySetInnerHTML: {\n                                  __html: formatText(docContent)\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 203,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 202,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 201,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 192,\n                          columnNumber: 35\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 186,\n                        columnNumber: 33\n                      }, this);\n                    }\n                  }\n\n                  // Fallback for other source formats\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 bg-green-50 dark:bg-gray-700 rounded border border-green-200 dark:border-gray-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-green-800 dark:text-green-200 text-xs\",\n                      children: [\"[Sumber \", index + 1, \"]\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 31\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-green-600 dark:text-green-400\",\n                      children: source\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 29\n                  }, this);\n                }).filter(Boolean)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-semibold text-green-700 dark:text-green-300 py-2\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center gap-2\",\n            children: \"\\uD83D\\uDCDA Sumber Referensi (0)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `text-xs mt-2 ${isBot ? \"text-green-600 dark:text-green-400\" : \"text-green-100 dark:text-green-200\"}`,\n        children: formatTime(timestamp)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_c = Message;\nexport default Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");", "map": {"version": 3, "names": ["React", "Accordion", "Accordi<PERSON><PERSON><PERSON><PERSON>", "AccordionItem", "AccordionTrigger", "jsxDEV", "_jsxDEV", "Message", "message", "text", "isBot", "timestamp", "sources", "isError", "summary", "suggestions", "sources_count", "is_greeting", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "formatText", "formatted", "replace", "className", "children", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "type", "collapsible", "value", "map", "source", "index", "filenameMatch", "match", "filename", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "includes", "isDocumentAnswer", "startsWith", "docNumber", "filter", "Boolean", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/Message.jsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n  AccordionTrigger,\n} from \"./components/ui/accordion\";\n\nconst Message = ({ message }) => {\n  const {\n    text,\n    isBot,\n    timestamp,\n    sources,\n    isError,\n    summary,\n    suggestions,\n    sources_count,\n    is_greeting,\n  } = message;\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString(\"id-ID\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  // Function to format text with basic markdown-like formatting\n  const formatText = (text) => {\n    if (!text) return text;\n\n    // Escape HTML first to prevent XSS\n    let formatted = text\n      .replace(/&/g, \"&amp;\")\n      .replace(/</g, \"&lt;\")\n      .replace(/>/g, \"&gt;\");\n\n    // Convert **bold** to <strong>\n    formatted = formatted.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\");\n\n    // Convert *italic* to <em>\n    formatted = formatted.replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n\n    // Convert line breaks to <br>\n    formatted = formatted.replace(/\\n/g, \"<br>\");\n\n    // Convert numbered lists (1. 2. 3.)\n    formatted = formatted.replace(/^(\\d+\\.\\s)/gm, \"<strong>$1</strong>\");\n\n    // Convert bullet points (- or *)\n    formatted = formatted.replace(/^[-*]\\s/gm, \"• \");\n\n    // Convert ## headers to bold\n    formatted = formatted.replace(/^##\\s(.+)$/gm, \"<strong>$1</strong>\");\n\n    return formatted;\n  };\n\n  return (\n    <div className={`flex ${isBot ? \"justify-start\" : \"justify-end\"}`}>\n      <div\n        className={`max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${\n          isBot\n            ? isError\n              ? \"bg-red-50 dark:bg-red-900 text-red-800 dark:text-red-200 border-2 border-red-300 dark:border-red-600\"\n              : \"bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-700 dark:to-gray-600 text-green-800 dark:text-green-200 border-2 border-green-300 dark:border-gray-500\"\n            : \"bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white shadow-lg\"\n        }`}\n      >\n        <div\n          className=\"text-sm sm:text-base leading-relaxed whitespace-pre-wrap\"\n          dangerouslySetInnerHTML={{ __html: formatText(text) }}\n        />\n\n        {/* Summary Section */}\n        {summary && isBot && (\n          <div className=\"mt-4 p-3 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\">\n            <h4 className=\"text-sm font-bold text-green-800 dark:text-green-200 mb-2 flex items-center\">\n              📝 Kesimpulan\n            </h4>\n            <div\n              className=\"text-sm text-green-700 dark:text-green-300 leading-relaxed\"\n              dangerouslySetInnerHTML={{ __html: formatText(summary) }}\n            />\n          </div>\n        )}\n\n        {/* Suggestions Section */}\n        {suggestions && isBot && (\n          <div className=\"mt-3 p-3 bg-gradient-to-r from-blue-50 to-green-50 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-blue-300 dark:border-gray-400\">\n            <h4 className=\"text-sm font-bold text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n              💡 Saran\n            </h4>\n            <div\n              className=\"text-sm text-blue-700 dark:text-blue-300 leading-relaxed\"\n              dangerouslySetInnerHTML={{ __html: formatText(suggestions) }}\n            />\n          </div>\n        )}\n\n        {/* Sources Section with Accordion */}\n        {isBot && !is_greeting && (\n          <div className=\"mt-3 pt-3 border-t border-green-400 dark:border-gray-500\">\n            {sources && sources.length > 0 ? (\n              <Accordion type=\"single\" collapsible className=\"w-full\">\n                <AccordionItem value=\"sources\" className=\"border-none\">\n                  <AccordionTrigger className=\"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2\">\n                    <span className=\"flex items-center gap-2\">\n                      📚 Sumber Referensi ({sources.length})\n                    </span>\n                  </AccordionTrigger>\n                  <AccordionContent className=\"pt-0 pb-2\">\n                    <div className=\"space-y-3\">\n                      {sources\n                        .map((source, index) => {\n                          // Check if this is a document-specific answer with filename\n                          const filenameMatch =\n                            source.match(/^(.+\\.docx): (.+)$/s);\n\n                          if (filenameMatch) {\n                            const filename = filenameMatch[1];\n                            const docContent = filenameMatch[2];\n\n                            // Skip if no relevant information\n                            if (\n                              docContent.includes(\"Tidak ada informasi relevan\")\n                            ) {\n                              return null;\n                            }\n\n                            return (\n                              <Accordion\n                                key={index}\n                                type=\"single\"\n                                collapsible\n                                className=\"w-full\"\n                              >\n                                <AccordionItem\n                                  value={`doc-${index}`}\n                                  className=\"border border-green-200 dark:border-gray-600 rounded-lg\"\n                                >\n                                  <AccordionTrigger className=\"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2 px-3\">\n                                    <span className=\"flex items-center gap-2\">\n                                      📄 {filename}\n                                    </span>\n                                  </AccordionTrigger>\n                                  <AccordionContent className=\"pt-0 pb-2 px-3\">\n                                    <div className=\"p-4 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\">\n                                      <div\n                                        className=\"text-sm text-green-700 dark:text-green-300 leading-relaxed\"\n                                        dangerouslySetInnerHTML={{\n                                          __html: formatText(docContent),\n                                        }}\n                                      />\n                                    </div>\n                                  </AccordionContent>\n                                </AccordionItem>\n                              </Accordion>\n                            );\n                          }\n\n                          // Check if this is a document-specific answer (starting with \"Dokumen X:\")\n                          const isDocumentAnswer =\n                            source.startsWith(\"Dokumen \");\n\n                          if (isDocumentAnswer) {\n                            // Extract document number and content\n                            const match = source.match(\n                              /^Dokumen (\\d+): (.+)$/s\n                            );\n                            if (match) {\n                              const docNumber = match[1];\n                              const docContent = match[2];\n\n                              // Skip if no relevant information\n                              if (\n                                docContent.includes(\n                                  \"Tidak ada informasi relevan\"\n                                )\n                              ) {\n                                return null;\n                              }\n\n                              return (\n                                <Accordion\n                                  key={index}\n                                  type=\"single\"\n                                  collapsible\n                                  className=\"w-full\"\n                                >\n                                  <AccordionItem\n                                    value={`doc-${docNumber}`}\n                                    className=\"border border-green-200 dark:border-gray-600 rounded-lg\"\n                                  >\n                                    <AccordionTrigger className=\"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2 px-3\">\n                                      <span className=\"flex items-center gap-2\">\n                                        📄 Dokumen {docNumber}\n                                      </span>\n                                    </AccordionTrigger>\n                                    <AccordionContent className=\"pt-0 pb-2 px-3\">\n                                      <div className=\"p-4 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\">\n                                        <div\n                                          className=\"text-sm text-green-700 dark:text-green-300 leading-relaxed\"\n                                          dangerouslySetInnerHTML={{\n                                            __html: formatText(docContent),\n                                          }}\n                                        />\n                                      </div>\n                                    </AccordionContent>\n                                  </AccordionItem>\n                                </Accordion>\n                              );\n                            }\n                          }\n\n                          // Fallback for other source formats\n                          return (\n                            <div\n                              key={index}\n                              className=\"p-2 bg-green-50 dark:bg-gray-700 rounded border border-green-200 dark:border-gray-600\"\n                            >\n                              <span className=\"font-medium text-green-800 dark:text-green-200 text-xs\">\n                                [Sumber {index + 1}]\n                              </span>{\" \"}\n                              <span className=\"text-xs text-green-600 dark:text-green-400\">\n                                {source}\n                              </span>\n                            </div>\n                          );\n                        })\n                        .filter(Boolean)}\n                    </div>\n                  </AccordionContent>\n                </AccordionItem>\n              </Accordion>\n            ) : (\n              <div className=\"text-xs font-semibold text-green-700 dark:text-green-300 py-2\">\n                <span className=\"flex items-center gap-2\">\n                  📚 Sumber Referensi (0)\n                </span>\n              </div>\n            )}\n          </div>\n        )}\n\n        <p\n          className={`text-xs mt-2 ${\n            isBot\n              ? \"text-green-600 dark:text-green-400\"\n              : \"text-green-100 dark:text-green-200\"\n          }`}\n        >\n          {formatTime(timestamp)}\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Message;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,gBAAgB,EAChBC,aAAa,EACbC,gBAAgB,QACX,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,OAAO,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC/B,MAAM;IACJC,IAAI;IACJC,KAAK;IACLC,SAAS;IACTC,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,WAAW;IACXC,aAAa;IACbC;EACF,CAAC,GAAGT,OAAO;EAEX,MAAMU,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAId,IAAI,IAAK;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAOA,IAAI;;IAEtB;IACA,IAAIe,SAAS,GAAGf,IAAI,CACjBgB,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;;IAExB;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;;IAEtE;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC;;IAE1D;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;IAE5C;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,cAAc,EAAE,qBAAqB,CAAC;;IAEpE;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;;IAEhD;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,cAAc,EAAE,qBAAqB,CAAC;IAEpE,OAAOD,SAAS;EAClB,CAAC;EAED,oBACElB,OAAA;IAAKoB,SAAS,EAAE,QAAQhB,KAAK,GAAG,eAAe,GAAG,aAAa,EAAG;IAAAiB,QAAA,eAChErB,OAAA;MACEoB,SAAS,EAAE,2FACThB,KAAK,GACDG,OAAO,GACL,sGAAsG,GACtG,mKAAmK,GACrK,yGAAyG,EAC5G;MAAAc,QAAA,gBAEHrB,OAAA;QACEoB,SAAS,EAAC,0DAA0D;QACpEE,uBAAuB,EAAE;UAAEC,MAAM,EAAEN,UAAU,CAACd,IAAI;QAAE;MAAE;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EAGDnB,OAAO,IAAIJ,KAAK,iBACfJ,OAAA;QAAKoB,SAAS,EAAC,oJAAoJ;QAAAC,QAAA,gBACjKrB,OAAA;UAAIoB,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAE5F;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UACEoB,SAAS,EAAC,4DAA4D;UACtEE,uBAAuB,EAAE;YAAEC,MAAM,EAAEN,UAAU,CAACT,OAAO;UAAE;QAAE;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAlB,WAAW,IAAIL,KAAK,iBACnBJ,OAAA;QAAKoB,SAAS,EAAC,+IAA+I;QAAAC,QAAA,gBAC5JrB,OAAA;UAAIoB,SAAS,EAAC,2EAA2E;UAAAC,QAAA,EAAC;QAE1F;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UACEoB,SAAS,EAAC,0DAA0D;UACpEE,uBAAuB,EAAE;YAAEC,MAAM,EAAEN,UAAU,CAACR,WAAW;UAAE;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAvB,KAAK,IAAI,CAACO,WAAW,iBACpBX,OAAA;QAAKoB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,EACtEf,OAAO,IAAIA,OAAO,CAACsB,MAAM,GAAG,CAAC,gBAC5B5B,OAAA,CAACL,SAAS;UAACkC,IAAI,EAAC,QAAQ;UAACC,WAAW;UAACV,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrDrB,OAAA,CAACH,aAAa;YAACkC,KAAK,EAAC,SAAS;YAACX,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACpDrB,OAAA,CAACF,gBAAgB;cAACsB,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC5GrB,OAAA;gBAAMoB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GAAC,iCACnB,EAACf,OAAO,CAACsB,MAAM,EAAC,GACvC;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACnB3B,OAAA,CAACJ,gBAAgB;cAACwB,SAAS,EAAC,WAAW;cAAAC,QAAA,eACrCrB,OAAA;gBAAKoB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBf,OAAO,CACL0B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;kBACtB;kBACA,MAAMC,aAAa,GACjBF,MAAM,CAACG,KAAK,CAAC,qBAAqB,CAAC;kBAErC,IAAID,aAAa,EAAE;oBACjB,MAAME,QAAQ,GAAGF,aAAa,CAAC,CAAC,CAAC;oBACjC,MAAMG,UAAU,GAAGH,aAAa,CAAC,CAAC,CAAC;;oBAEnC;oBACA,IACEG,UAAU,CAACC,QAAQ,CAAC,6BAA6B,CAAC,EAClD;sBACA,OAAO,IAAI;oBACb;oBAEA,oBACEvC,OAAA,CAACL,SAAS;sBAERkC,IAAI,EAAC,QAAQ;sBACbC,WAAW;sBACXV,SAAS,EAAC,QAAQ;sBAAAC,QAAA,eAElBrB,OAAA,CAACH,aAAa;wBACZkC,KAAK,EAAE,OAAOG,KAAK,EAAG;wBACtBd,SAAS,EAAC,yDAAyD;wBAAAC,QAAA,gBAEnErB,OAAA,CAACF,gBAAgB;0BAACsB,SAAS,EAAC,uFAAuF;0BAAAC,QAAA,eACjHrB,OAAA;4BAAMoB,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,GAAC,eACrC,EAACgB,QAAQ;0BAAA;4BAAAb,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACS,CAAC,eACnB3B,OAAA,CAACJ,gBAAgB;0BAACwB,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,eAC1CrB,OAAA;4BAAKoB,SAAS,EAAC,+IAA+I;4BAAAC,QAAA,eAC5JrB,OAAA;8BACEoB,SAAS,EAAC,4DAA4D;8BACtEE,uBAAuB,EAAE;gCACvBC,MAAM,EAAEN,UAAU,CAACqB,UAAU;8BAC/B;4BAAE;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACU,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC,GAxBXO,KAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyBD,CAAC;kBAEhB;;kBAEA;kBACA,MAAMa,gBAAgB,GACpBP,MAAM,CAACQ,UAAU,CAAC,UAAU,CAAC;kBAE/B,IAAID,gBAAgB,EAAE;oBACpB;oBACA,MAAMJ,KAAK,GAAGH,MAAM,CAACG,KAAK,CACxB,wBACF,CAAC;oBACD,IAAIA,KAAK,EAAE;sBACT,MAAMM,SAAS,GAAGN,KAAK,CAAC,CAAC,CAAC;sBAC1B,MAAME,UAAU,GAAGF,KAAK,CAAC,CAAC,CAAC;;sBAE3B;sBACA,IACEE,UAAU,CAACC,QAAQ,CACjB,6BACF,CAAC,EACD;wBACA,OAAO,IAAI;sBACb;sBAEA,oBACEvC,OAAA,CAACL,SAAS;wBAERkC,IAAI,EAAC,QAAQ;wBACbC,WAAW;wBACXV,SAAS,EAAC,QAAQ;wBAAAC,QAAA,eAElBrB,OAAA,CAACH,aAAa;0BACZkC,KAAK,EAAE,OAAOW,SAAS,EAAG;0BAC1BtB,SAAS,EAAC,yDAAyD;0BAAAC,QAAA,gBAEnErB,OAAA,CAACF,gBAAgB;4BAACsB,SAAS,EAAC,uFAAuF;4BAAAC,QAAA,eACjHrB,OAAA;8BAAMoB,SAAS,EAAC,yBAAyB;8BAAAC,QAAA,GAAC,uBAC7B,EAACqB,SAAS;4BAAA;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACS,CAAC,eACnB3B,OAAA,CAACJ,gBAAgB;4BAACwB,SAAS,EAAC,gBAAgB;4BAAAC,QAAA,eAC1CrB,OAAA;8BAAKoB,SAAS,EAAC,+IAA+I;8BAAAC,QAAA,eAC5JrB,OAAA;gCACEoB,SAAS,EAAC,4DAA4D;gCACtEE,uBAAuB,EAAE;kCACvBC,MAAM,EAAEN,UAAU,CAACqB,UAAU;gCAC/B;8BAAE;gCAAAd,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACU,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN;sBAAC,GAxBXO,KAAK;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAyBD,CAAC;oBAEhB;kBACF;;kBAEA;kBACA,oBACE3B,OAAA;oBAEEoB,SAAS,EAAC,uFAAuF;oBAAAC,QAAA,gBAEjGrB,OAAA;sBAAMoB,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,GAAC,UAC/D,EAACa,KAAK,GAAG,CAAC,EAAC,GACrB;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAAC,GAAG,eACX3B,OAAA;sBAAMoB,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EACzDY;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GARFO,KAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASP,CAAC;gBAEV,CAAC,CAAC,CACDgB,MAAM,CAACC,OAAO;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,gBAEZ3B,OAAA;UAAKoB,SAAS,EAAC,+DAA+D;UAAAC,QAAA,eAC5ErB,OAAA;YAAMoB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAED3B,OAAA;QACEoB,SAAS,EAAE,gBACThB,KAAK,GACD,oCAAoC,GACpC,oCAAoC,EACvC;QAAAiB,QAAA,EAEFT,UAAU,CAACP,SAAS;MAAC;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACkB,EAAA,GA1PI5C,OAAO;AA4Pb,eAAeA,OAAO;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}