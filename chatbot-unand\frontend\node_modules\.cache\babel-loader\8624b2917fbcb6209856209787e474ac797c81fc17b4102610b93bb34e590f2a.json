{"ast": null, "code": "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({\n        width: element.offsetWidth,\n        height: element.offsetHeight\n      });\n      const resizeObserver = new ResizeObserver(entries => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({\n          width,\n          height\n        });\n      });\n      resizeObserver.observe(element, {\n        box: \"border-box\"\n      });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport { useSize };", "map": {"version": 3, "names": ["React", "useLayoutEffect", "useSize", "element", "size", "setSize", "useState", "width", "offsetWidth", "height", "offsetHeight", "resizeObserver", "ResizeObserver", "entries", "Array", "isArray", "length", "entry", "borderSizeEntry", "borderSize", "observe", "box", "unobserve"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-use-size\\src\\use-size.tsx"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n"], "mappings": ";AAEA,YAAYA,KAAA,MAAW;AACvB,SAASC,eAAA,QAAuB;AAEhC,SAASC,QAAQC,OAAA,EAA6B;EAC5C,MAAM,CAACC,IAAA,EAAMC,OAAO,IAAUL,KAAA,CAAAM,QAAA,CAAwD,MAAS;EAE/FL,eAAA,CAAgB,MAAM;IACpB,IAAIE,OAAA,EAAS;MAEXE,OAAA,CAAQ;QAAEE,KAAA,EAAOJ,OAAA,CAAQK,WAAA;QAAaC,MAAA,EAAQN,OAAA,CAAQO;MAAa,CAAC;MAEpE,MAAMC,cAAA,GAAiB,IAAIC,cAAA,CAAgBC,OAAA,IAAY;QACrD,IAAI,CAACC,KAAA,CAAMC,OAAA,CAAQF,OAAO,GAAG;UAC3B;QACF;QAIA,IAAI,CAACA,OAAA,CAAQG,MAAA,EAAQ;UACnB;QACF;QAEA,MAAMC,KAAA,GAAQJ,OAAA,CAAQ,CAAC;QACvB,IAAIN,KAAA;QACJ,IAAIE,MAAA;QAEJ,IAAI,mBAAmBQ,KAAA,EAAO;UAC5B,MAAMC,eAAA,GAAkBD,KAAA,CAAM,eAAe;UAE7C,MAAME,UAAA,GAAaL,KAAA,CAAMC,OAAA,CAAQG,eAAe,IAAIA,eAAA,CAAgB,CAAC,IAAIA,eAAA;UACzEX,KAAA,GAAQY,UAAA,CAAW,YAAY;UAC/BV,MAAA,GAASU,UAAA,CAAW,WAAW;QACjC,OAAO;UAGLZ,KAAA,GAAQJ,OAAA,CAAQK,WAAA;UAChBC,MAAA,GAASN,OAAA,CAAQO,YAAA;QACnB;QAEAL,OAAA,CAAQ;UAAEE,KAAA;UAAOE;QAAO,CAAC;MAC3B,CAAC;MAEDE,cAAA,CAAeS,OAAA,CAAQjB,OAAA,EAAS;QAAEkB,GAAA,EAAK;MAAa,CAAC;MAErD,OAAO,MAAMV,cAAA,CAAeW,SAAA,CAAUnB,OAAO;IAC/C,OAAO;MAGLE,OAAA,CAAQ,MAAS;IACnB;EACF,GAAG,CAACF,OAAO,CAAC;EAEZ,OAAOC,IAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}