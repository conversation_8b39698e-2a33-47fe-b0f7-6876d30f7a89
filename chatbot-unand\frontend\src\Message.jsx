import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "./components/ui/accordion";

const Message = ({ message }) => {
  const {
    text,
    isBot,
    timestamp,
    sources,
    isError,
    summary,
    suggestions,
    sources_count,
  } = message;

  const formatTime = (date) => {
    return date.toLocaleTimeString("id-ID", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Function to format text with basic markdown-like formatting
  const formatText = (text) => {
    if (!text) return text;

    // Escape HTML first to prevent XSS
    let formatted = text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;");

    // Convert **bold** to <strong>
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");

    // Convert *italic* to <em>
    formatted = formatted.replace(/\*(.*?)\*/g, "<em>$1</em>");

    // Convert line breaks to <br>
    formatted = formatted.replace(/\n/g, "<br>");

    // Convert numbered lists (1. 2. 3.)
    formatted = formatted.replace(/^(\d+\.\s)/gm, "<strong>$1</strong>");

    // Convert bullet points (- or *)
    formatted = formatted.replace(/^[-*]\s/gm, "• ");

    // Convert ## headers to bold
    formatted = formatted.replace(/^##\s(.+)$/gm, "<strong>$1</strong>");

    return formatted;
  };

  return (
    <div className={`flex ${isBot ? "justify-start" : "justify-end"}`}>
      <div
        className={`max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${
          isBot
            ? isError
              ? "bg-red-50 dark:bg-red-900 text-red-800 dark:text-red-200 border-2 border-red-300 dark:border-red-600"
              : "bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-700 dark:to-gray-600 text-green-800 dark:text-green-200 border-2 border-green-300 dark:border-gray-500"
            : "bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white shadow-lg"
        }`}
      >
        <div
          className="text-sm sm:text-base leading-relaxed whitespace-pre-wrap"
          dangerouslySetInnerHTML={{ __html: formatText(text) }}
        />

        {/* Summary Section */}
        {summary && isBot && (
          <div className="mt-4 p-3 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400">
            <h4 className="text-sm font-bold text-green-800 dark:text-green-200 mb-2 flex items-center">
              📝 Kesimpulan
            </h4>
            <div
              className="text-sm text-green-700 dark:text-green-300 leading-relaxed"
              dangerouslySetInnerHTML={{ __html: formatText(summary) }}
            />
          </div>
        )}

        {/* Suggestions Section */}
        {suggestions && isBot && (
          <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-green-50 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-blue-300 dark:border-gray-400">
            <h4 className="text-sm font-bold text-blue-800 dark:text-blue-200 mb-2 flex items-center">
              💡 Saran
            </h4>
            <div
              className="text-sm text-blue-700 dark:text-blue-300 leading-relaxed"
              dangerouslySetInnerHTML={{ __html: formatText(suggestions) }}
            />
          </div>
        )}

        {/* Sources Section with Accordion */}
        {isBot && (
          <div className="mt-3 pt-3 border-t border-green-400 dark:border-gray-500">
            {sources && sources.length > 0 ? (
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="sources" className="border-none">
                  <AccordionTrigger className="text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2">
                    <span className="flex items-center gap-2">
                      📚 Sumber Referensi ({sources.length})
                    </span>
                  </AccordionTrigger>
                  <AccordionContent className="pt-0 pb-2">
                    <div className="space-y-3">
                      {sources
                        .map((source, index) => {
                          // Check if this is a document-specific answer with filename
                          const filenameMatch =
                            source.match(/^(.+\.docx): (.+)$/s);

                          if (filenameMatch) {
                            const filename = filenameMatch[1];
                            const docContent = filenameMatch[2];

                            // Skip if no relevant information
                            if (
                              docContent.includes("Tidak ada informasi relevan")
                            ) {
                              return null;
                            }

                            return (
                              <Accordion
                                key={index}
                                type="single"
                                collapsible
                                className="w-full"
                              >
                                <AccordionItem
                                  value={`doc-${index}`}
                                  className="border border-green-200 dark:border-gray-600 rounded-lg"
                                >
                                  <AccordionTrigger className="text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2 px-3">
                                    <span className="flex items-center gap-2">
                                      📄 {filename}
                                    </span>
                                  </AccordionTrigger>
                                  <AccordionContent className="pt-0 pb-2 px-3">
                                    <div className="p-4 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400">
                                      <div
                                        className="text-sm text-green-700 dark:text-green-300 leading-relaxed"
                                        dangerouslySetInnerHTML={{
                                          __html: formatText(docContent),
                                        }}
                                      />
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              </Accordion>
                            );
                          }

                          // Check if this is a document-specific answer (starting with "Dokumen X:")
                          const isDocumentAnswer =
                            source.startsWith("Dokumen ");

                          if (isDocumentAnswer) {
                            // Extract document number and content
                            const match = source.match(
                              /^Dokumen (\d+): (.+)$/s
                            );
                            if (match) {
                              const docNumber = match[1];
                              const docContent = match[2];

                              // Skip if no relevant information
                              if (
                                docContent.includes(
                                  "Tidak ada informasi relevan"
                                )
                              ) {
                                return null;
                              }

                              return (
                                <Accordion
                                  key={index}
                                  type="single"
                                  collapsible
                                  className="w-full"
                                >
                                  <AccordionItem
                                    value={`doc-${docNumber}`}
                                    className="border border-green-200 dark:border-gray-600 rounded-lg"
                                  >
                                    <AccordionTrigger className="text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2 px-3">
                                      <span className="flex items-center gap-2">
                                        📄 Dokumen {docNumber}
                                      </span>
                                    </AccordionTrigger>
                                    <AccordionContent className="pt-0 pb-2 px-3">
                                      <div className="p-4 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400">
                                        <div
                                          className="text-sm text-green-700 dark:text-green-300 leading-relaxed"
                                          dangerouslySetInnerHTML={{
                                            __html: formatText(docContent),
                                          }}
                                        />
                                      </div>
                                    </AccordionContent>
                                  </AccordionItem>
                                </Accordion>
                              );
                            }
                          }

                          // Fallback for other source formats
                          return (
                            <div
                              key={index}
                              className="p-2 bg-green-50 dark:bg-gray-700 rounded border border-green-200 dark:border-gray-600"
                            >
                              <span className="font-medium text-green-800 dark:text-green-200 text-xs">
                                [Sumber {index + 1}]
                              </span>{" "}
                              <span className="text-xs text-green-600 dark:text-green-400">
                                {source}
                              </span>
                            </div>
                          );
                        })
                        .filter(Boolean)}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ) : (
              <div className="text-xs font-semibold text-green-700 dark:text-green-300 py-2">
                <span className="flex items-center gap-2">
                  📚 Sumber Referensi (0)
                </span>
              </div>
            )}
          </div>
        )}

        <p
          className={`text-xs mt-2 ${
            isBot
              ? "text-green-600 dark:text-green-400"
              : "text-green-100 dark:text-green-200"
          }`}
        >
          {formatTime(timestamp)}
        </p>
      </div>
    </div>
  );
};

export default Message;
