{"ast": null, "code": "\"use client\";\n\n// src/presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// src/use-state-machine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = props => {\n  const {\n    present,\n    children\n  } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({\n    present: presence.isPresent\n  }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, {\n    ref\n  }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef(null);\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = event => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = event => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback(node2 => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\nexport { Presence, Root };", "map": {"version": 3, "names": ["React2", "useComposedRefs", "useLayoutEffect", "React", "useStateMachine", "initialState", "machine", "useReducer", "state", "event", "nextState", "Presence", "props", "present", "children", "presence", "usePresence", "child", "isPresent", "Children", "only", "ref", "getElementRef", "forceMount", "cloneElement", "displayName", "node", "setNode", "useState", "stylesRef", "useRef", "prevPresentRef", "prevAnimationNameRef", "send", "mounted", "UNMOUNT", "ANIMATION_OUT", "unmountSuspended", "MOUNT", "ANIMATION_END", "unmounted", "useEffect", "currentAnimationName", "getAnimationName", "current", "styles", "wasPresent", "hasPresentChanged", "prevAnimationName", "display", "isAnimating", "timeoutId", "ownerWindow", "ownerDocument", "defaultView", "window", "handleAnimationEnd", "isCurrentAnimation", "includes", "animationName", "target", "currentFillMode", "style", "animationFillMode", "setTimeout", "handleAnimationStart", "addEventListener", "clearTimeout", "removeEventListener", "useCallback", "node2", "getComputedStyle", "element", "getter", "Object", "getOwnPropertyDescriptor", "get", "<PERSON><PERSON><PERSON><PERSON>", "isReactWarning", "Root"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-presence\\src\\presence.tsx", "C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-presence\\src\\use-state-machine.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './use-state-machine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;\n\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration | null>(null);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId: number;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied a frame after the\n          // animation ends, creating a flash of visible content. By setting the\n          // animation fill mode to \"forwards\", we force the node to keep the\n          // styles of the last keyframe, removing the flash.\n          //\n          // Previously we flushed the update via ReactDom.flushSync, but with\n          // exit animations this resulted in the node being removed from the\n          // DOM before the synthetic animationEnd event was dispatched, meaning\n          // user-provided event handlers would not be called.\n          // https://github.com/radix-ui/primitives/pull/1849\n          send('ANIMATION_END');\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = 'forwards';\n            // Reset the style after the node had time to unmount (for cases\n            // where the component chooses not to unmount). Doing this any\n            // sooner than `setTimeout` (e.g. with `requestAnimationFrame`)\n            // still causes a flash.\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === 'forwards') {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      stylesRef.current = node ? getComputedStyle(node) : null;\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles: CSSStyleDeclaration | null) {\n  return styles?.animationName || 'none';\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement<{ ref?: React.Ref<unknown> }>) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Presence;\n\nexport {\n  Presence,\n  //\n  Root,\n};\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "mappings": ";;;AAAA,YAAYA,MAAA,MAAW;AACvB,SAASC,eAAA,QAAuB;AAChC,SAASC,eAAA,QAAuB;;;ACFhC,YAAYC,KAAA,MAAW;AAWhB,SAASC,gBACdC,YAAA,EACAC,OAAA,EACA;EACA,OAAaH,KAAA,CAAAI,UAAA,CAAW,CAACC,KAAA,EAAwBC,KAAA,KAA4C;IAC3F,MAAMC,SAAA,GAAaJ,OAAA,CAAQE,KAAK,EAAUC,KAAK;IAC/C,OAAOC,SAAA,IAAaF,KAAA;EACtB,GAAGH,YAAY;AACjB;;;ADTA,IAAMM,QAAA,GAAqCC,KAAA,IAAU;EACnD,MAAM;IAAEC,OAAA;IAASC;EAAS,IAAIF,KAAA;EAC9B,MAAMG,QAAA,GAAWC,WAAA,CAAYH,OAAO;EAEpC,MAAMI,KAAA,GACJ,OAAOH,QAAA,KAAa,aAChBA,QAAA,CAAS;IAAED,OAAA,EAASE,QAAA,CAASG;EAAU,CAAC,IAClClB,MAAA,CAAAmB,QAAA,CAASC,IAAA,CAAKN,QAAQ;EAGlC,MAAMO,GAAA,GAAMpB,eAAA,CAAgBc,QAAA,CAASM,GAAA,EAAKC,aAAA,CAAcL,KAAK,CAAC;EAC9D,MAAMM,UAAA,GAAa,OAAOT,QAAA,KAAa;EACvC,OAAOS,UAAA,IAAcR,QAAA,CAASG,SAAA,GAAkBlB,MAAA,CAAAwB,YAAA,CAAaP,KAAA,EAAO;IAAEI;EAAI,CAAC,IAAI;AACjF;AAEAV,QAAA,CAASc,WAAA,GAAc;AAMvB,SAAST,YAAYH,OAAA,EAAkB;EACrC,MAAM,CAACa,IAAA,EAAMC,OAAO,IAAU3B,MAAA,CAAA4B,QAAA,CAAsB;EACpD,MAAMC,SAAA,GAAkB7B,MAAA,CAAA8B,MAAA,CAAmC,IAAI;EAC/D,MAAMC,cAAA,GAAuB/B,MAAA,CAAA8B,MAAA,CAAOjB,OAAO;EAC3C,MAAMmB,oBAAA,GAA6BhC,MAAA,CAAA8B,MAAA,CAAe,MAAM;EACxD,MAAMzB,YAAA,GAAeQ,OAAA,GAAU,YAAY;EAC3C,MAAM,CAACL,KAAA,EAAOyB,IAAI,IAAI7B,eAAA,CAAgBC,YAAA,EAAc;IAClD6B,OAAA,EAAS;MACPC,OAAA,EAAS;MACTC,aAAA,EAAe;IACjB;IACAC,gBAAA,EAAkB;MAChBC,KAAA,EAAO;MACPC,aAAA,EAAe;IACjB;IACAC,SAAA,EAAW;MACTF,KAAA,EAAO;IACT;EACF,CAAC;EAEKtC,MAAA,CAAAyC,SAAA,CAAU,MAAM;IACpB,MAAMC,oBAAA,GAAuBC,gBAAA,CAAiBd,SAAA,CAAUe,OAAO;IAC/DZ,oBAAA,CAAqBY,OAAA,GAAUpC,KAAA,KAAU,YAAYkC,oBAAA,GAAuB;EAC9E,GAAG,CAAClC,KAAK,CAAC;EAEVN,eAAA,CAAgB,MAAM;IACpB,MAAM2C,MAAA,GAAShB,SAAA,CAAUe,OAAA;IACzB,MAAME,UAAA,GAAaf,cAAA,CAAea,OAAA;IAClC,MAAMG,iBAAA,GAAoBD,UAAA,KAAejC,OAAA;IAEzC,IAAIkC,iBAAA,EAAmB;MACrB,MAAMC,iBAAA,GAAoBhB,oBAAA,CAAqBY,OAAA;MAC/C,MAAMF,oBAAA,GAAuBC,gBAAA,CAAiBE,MAAM;MAEpD,IAAIhC,OAAA,EAAS;QACXoB,IAAA,CAAK,OAAO;MACd,WAAWS,oBAAA,KAAyB,UAAUG,MAAA,EAAQI,OAAA,KAAY,QAAQ;QAGxEhB,IAAA,CAAK,SAAS;MAChB,OAAO;QAOL,MAAMiB,WAAA,GAAcF,iBAAA,KAAsBN,oBAAA;QAE1C,IAAII,UAAA,IAAcI,WAAA,EAAa;UAC7BjB,IAAA,CAAK,eAAe;QACtB,OAAO;UACLA,IAAA,CAAK,SAAS;QAChB;MACF;MAEAF,cAAA,CAAea,OAAA,GAAU/B,OAAA;IAC3B;EACF,GAAG,CAACA,OAAA,EAASoB,IAAI,CAAC;EAElB/B,eAAA,CAAgB,MAAM;IACpB,IAAIwB,IAAA,EAAM;MACR,IAAIyB,SAAA;MACJ,MAAMC,WAAA,GAAc1B,IAAA,CAAK2B,aAAA,CAAcC,WAAA,IAAeC,MAAA;MAMtD,MAAMC,kBAAA,GAAsB/C,KAAA,IAA0B;QACpD,MAAMiC,oBAAA,GAAuBC,gBAAA,CAAiBd,SAAA,CAAUe,OAAO;QAC/D,MAAMa,kBAAA,GAAqBf,oBAAA,CAAqBgB,QAAA,CAASjD,KAAA,CAAMkD,aAAa;QAC5E,IAAIlD,KAAA,CAAMmD,MAAA,KAAWlC,IAAA,IAAQ+B,kBAAA,EAAoB;UAW/CxB,IAAA,CAAK,eAAe;UACpB,IAAI,CAACF,cAAA,CAAea,OAAA,EAAS;YAC3B,MAAMiB,eAAA,GAAkBnC,IAAA,CAAKoC,KAAA,CAAMC,iBAAA;YACnCrC,IAAA,CAAKoC,KAAA,CAAMC,iBAAA,GAAoB;YAK/BZ,SAAA,GAAYC,WAAA,CAAYY,UAAA,CAAW,MAAM;cACvC,IAAItC,IAAA,CAAKoC,KAAA,CAAMC,iBAAA,KAAsB,YAAY;gBAC/CrC,IAAA,CAAKoC,KAAA,CAAMC,iBAAA,GAAoBF,eAAA;cACjC;YACF,CAAC;UACH;QACF;MACF;MACA,MAAMI,oBAAA,GAAwBxD,KAAA,IAA0B;QACtD,IAAIA,KAAA,CAAMmD,MAAA,KAAWlC,IAAA,EAAM;UAEzBM,oBAAA,CAAqBY,OAAA,GAAUD,gBAAA,CAAiBd,SAAA,CAAUe,OAAO;QACnE;MACF;MACAlB,IAAA,CAAKwC,gBAAA,CAAiB,kBAAkBD,oBAAoB;MAC5DvC,IAAA,CAAKwC,gBAAA,CAAiB,mBAAmBV,kBAAkB;MAC3D9B,IAAA,CAAKwC,gBAAA,CAAiB,gBAAgBV,kBAAkB;MACxD,OAAO,MAAM;QACXJ,WAAA,CAAYe,YAAA,CAAahB,SAAS;QAClCzB,IAAA,CAAK0C,mBAAA,CAAoB,kBAAkBH,oBAAoB;QAC/DvC,IAAA,CAAK0C,mBAAA,CAAoB,mBAAmBZ,kBAAkB;QAC9D9B,IAAA,CAAK0C,mBAAA,CAAoB,gBAAgBZ,kBAAkB;MAC7D;IACF,OAAO;MAGLvB,IAAA,CAAK,eAAe;IACtB;EACF,GAAG,CAACP,IAAA,EAAMO,IAAI,CAAC;EAEf,OAAO;IACLf,SAAA,EAAW,CAAC,WAAW,kBAAkB,EAAEwC,QAAA,CAASlD,KAAK;IACzDa,GAAA,EAAWrB,MAAA,CAAAqE,WAAA,CAAaC,KAAA,IAAsB;MAC5CzC,SAAA,CAAUe,OAAA,GAAU0B,KAAA,GAAOC,gBAAA,CAAiBD,KAAI,IAAI;MACpD3C,OAAA,CAAQ2C,KAAI;IACd,GAAG,EAAE;EACP;AACF;AAIA,SAAS3B,iBAAiBE,MAAA,EAAoC;EAC5D,OAAOA,MAAA,EAAQc,aAAA,IAAiB;AAClC;AAOA,SAASrC,cAAckD,OAAA,EAA2D;EAEhF,IAAIC,MAAA,GAASC,MAAA,CAAOC,wBAAA,CAAyBH,OAAA,CAAQ5D,KAAA,EAAO,KAAK,GAAGgE,GAAA;EACpE,IAAIC,OAAA,GAAUJ,MAAA,IAAU,oBAAoBA,MAAA,IAAUA,MAAA,CAAOK,cAAA;EAC7D,IAAID,OAAA,EAAS;IACX,OAAQL,OAAA,CAAgBnD,GAAA;EAC1B;EAGAoD,MAAA,GAASC,MAAA,CAAOC,wBAAA,CAAyBH,OAAA,EAAS,KAAK,GAAGI,GAAA;EAC1DC,OAAA,GAAUJ,MAAA,IAAU,oBAAoBA,MAAA,IAAUA,MAAA,CAAOK,cAAA;EACzD,IAAID,OAAA,EAAS;IACX,OAAOL,OAAA,CAAQ5D,KAAA,CAAMS,GAAA;EACvB;EAGA,OAAOmD,OAAA,CAAQ5D,KAAA,CAAMS,GAAA,IAAQmD,OAAA,CAAgBnD,GAAA;AAC/C;AAEA,IAAM0D,IAAA,GAAOpE,QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}