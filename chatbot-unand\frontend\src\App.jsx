import React, { useState } from "react";
import ChatWindow from "./ChatWindow";
import ChatS<PERSON>bar from "./ChatSidebar";
import TelegramButton from "./TelegramButton";
import ThemeToggle from "./components/ThemeToggle";
import { ThemeProvider } from "./contexts/ThemeContext";
import { getSessionMessages } from "./api";
import "./index.css"; // Pastikan Tailwind CSS diimpor

function App() {
  const [currentSessionId, setCurrentSessionId] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!",
      isBot: true,
      timestamp: new Date(),
    },
  ]);

  const handleSessionSelect = async (sessionId) => {
    try {
      setCurrentSessionId(sessionId);
      const sessionMessages = await getSessionMessages(sessionId);

      // Convert API messages to frontend format
      const convertedMessages = sessionMessages.map((msg) => ({
        id: msg.id,
        text: msg.content,
        isBot: msg.message_type === "bot",
        timestamp: new Date(msg.timestamp),
        sources: msg.sources,
        summary: msg.summary,
      }));

      setMessages(convertedMessages);
      // Close sidebar on mobile after selecting session
      if (window.innerWidth < 1024) {
        setIsSidebarOpen(false);
      }
    } catch (error) {
      console.error("Error loading session messages:", error);
      alert("Gagal memuat riwayat percakapan");
    }
  };

  const handleNewChat = () => {
    setCurrentSessionId(null);
    setMessages([
      {
        id: 1,
        text: "Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!",
        isBot: true,
        timestamp: new Date(),
      },
    ]);
    // Close sidebar on mobile after new chat
    if (window.innerWidth < 1024) {
      setIsSidebarOpen(false);
    }
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <ThemeProvider>
      <div className="h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex overflow-hidden relative">
        {/* Mobile Overlay */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div
          className={`fixed lg:static lg:translate-x-0 transition-transform duration-300 ease-in-out z-50 w-80 ${
            isSidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <ChatSidebar
            currentSessionId={currentSessionId}
            onSessionSelect={handleSessionSelect}
            onNewChat={handleNewChat}
            onClose={() => setIsSidebarOpen(false)}
          />
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Fixed Header */}
          <div className="border-b-2 border-green-600 dark:border-green-400 p-3 lg:p-6 shadow-lg flex-shrink-0 bg-transparent">
            {/* Mobile Header */}
            <div className="lg:hidden grid grid-cols-3 items-center mb-2">
              {/* Left: Menu Button */}
              <div className="flex justify-start">
                <button
                  onClick={toggleSidebar}
                  className="p-2 rounded-lg bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </button>
              </div>

              {/* Center: Logo and Title */}
              <div className="flex items-center justify-center gap-2">
                <img
                  src="/lambang-unand.jpg"
                  alt="Logo UNAND"
                  className="w-8 h-8 object-contain rounded border border-green-600 dark:border-green-400"
                />
                <h1 className="text-lg font-bold text-green-700 dark:text-green-300">
                  TANYO UNAND
                </h1>
              </div>

              {/* Right: Theme Toggle */}
              <div className="flex justify-end">
                <ThemeToggle />
              </div>
            </div>

            {/* Desktop Header */}
            <div className="hidden lg:grid lg:grid-cols-3 items-center h-19 w-full">
              {/* Left Section: Logo and Title */}
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0">
                  <img
                    src="/lambang-unand.jpg"
                    alt="Logo Universitas Andalas"
                    className="w-14 h-14 object-contain rounded-lg shadow-md border-2 border-green-600 dark:border-green-400"
                  />
                </div>
                <div className="flex flex-col justify-center">
                  <h1 className="text-xl font-bold text-green-700 dark:text-green-300 leading-tight">
                    TANYO UNAND
                  </h1>
                  <p className="text-green-600 dark:text-green-400 text-sm font-medium leading-tight">
                    Tampaik batanyo Seputar Universitas Andalas
                  </p>
                  <p className="text-yellow-700 dark:text-yellow-400 text-xs font-bold tracking-wider uppercase mt-1">
                    "UNTUK KEDJAJAAN BANGSA"
                  </p>
                </div>
              </div>

              {/* Center Section: Empty for balance */}
              <div className="flex items-center justify-center"></div>

              {/* Right Section: Theme Toggle */}
              <div className="flex items-center justify-end">
                <ThemeToggle />
              </div>
            </div>
          </div>

          {/* Chat Window */}
          <div className="flex-1 min-h-0">
            <ChatWindow
              messages={messages}
              setMessages={setMessages}
              currentSessionId={currentSessionId}
              setCurrentSessionId={setCurrentSessionId}
            />
          </div>
        </div>

        {/* Telegram Button */}
        <TelegramButton />
      </div>
    </ThemeProvider>
  );
}

export default App;
