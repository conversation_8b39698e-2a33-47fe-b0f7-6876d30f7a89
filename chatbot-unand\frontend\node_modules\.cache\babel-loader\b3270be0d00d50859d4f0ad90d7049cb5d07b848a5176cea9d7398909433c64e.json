{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\Message.jsx\";\nimport React from \"react\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"./components/ui/accordion\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Message = ({\n  message\n}) => {\n  const {\n    text,\n    isBot,\n    timestamp,\n    sources,\n    isError,\n    summary,\n    suggestions\n  } = message;\n  const formatTime = date => {\n    return date.toLocaleTimeString(\"id-ID\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n\n  // Function to format text with basic markdown-like formatting\n  const formatText = text => {\n    if (!text) return text;\n\n    // Escape HTML first to prevent XSS\n    let formatted = text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n\n    // Convert **bold** to <strong>\n    formatted = formatted.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\");\n\n    // Convert *italic* to <em>\n    formatted = formatted.replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n\n    // Convert line breaks to <br>\n    formatted = formatted.replace(/\\n/g, \"<br>\");\n\n    // Convert numbered lists (1. 2. 3.)\n    formatted = formatted.replace(/^(\\d+\\.\\s)/gm, \"<strong>$1</strong>\");\n\n    // Convert bullet points (- or *)\n    formatted = formatted.replace(/^[-*]\\s/gm, \"• \");\n\n    // Convert ## headers to bold\n    formatted = formatted.replace(/^##\\s(.+)$/gm, \"<strong>$1</strong>\");\n    return formatted;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isBot ? \"justify-start\" : \"justify-end\"}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${isBot ? isError ? \"bg-red-50 dark:bg-red-900 text-red-800 dark:text-red-200 border-2 border-red-300 dark:border-red-600\" : \"bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-700 dark:to-gray-600 text-green-800 dark:text-green-200 border-2 border-green-300 dark:border-gray-500\" : \"bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white shadow-lg\"}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm sm:text-base leading-relaxed whitespace-pre-wrap\",\n        dangerouslySetInnerHTML: {\n          __html: formatText(text)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), summary && isBot && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-3 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-bold text-green-800 dark:text-green-200 mb-2 flex items-center\",\n          children: \"\\uD83D\\uDCDD Kesimpulan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-green-700 dark:text-green-300 leading-relaxed\",\n          dangerouslySetInnerHTML: {\n            __html: formatText(summary)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this), sources && sources.length > 0 && isBot && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 pt-3 border-t border-green-400 dark:border-gray-500\",\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          type: \"single\",\n          collapsible: true,\n          className: \"w-full\",\n          children: /*#__PURE__*/_jsxDEV(AccordionItem, {\n            value: \"sources\",\n            className: \"border-none\",\n            children: [/*#__PURE__*/_jsxDEV(AccordionTrigger, {\n              className: \"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [\"\\uD83D\\uDCDA Sumber Referensi (\", sources.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(AccordionContent, {\n              className: \"pt-0 pb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-xs space-y-2 text-green-600 dark:text-green-400\",\n                children: sources.map((source, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"p-2 bg-green-50 dark:bg-gray-700 rounded border border-green-200 dark:border-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-green-800 dark:text-green-200\",\n                    children: [\"[\", index + 1, \"]\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 25\n                  }, this), \" \", source]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `text-xs mt-2 ${isBot ? \"text-green-600 dark:text-green-400\" : \"text-green-100 dark:text-green-200\"}`,\n        children: formatTime(timestamp)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_c = Message;\nexport default Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");", "map": {"version": 3, "names": ["React", "Accordion", "Accordi<PERSON><PERSON><PERSON><PERSON>", "AccordionItem", "AccordionTrigger", "jsxDEV", "_jsxDEV", "Message", "message", "text", "isBot", "timestamp", "sources", "isError", "summary", "suggestions", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "formatText", "formatted", "replace", "className", "children", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "type", "collapsible", "value", "map", "source", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/Message.jsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n  AccordionTrigger,\n} from \"./components/ui/accordion\";\n\nconst Message = ({ message }) => {\n  const { text, isBot, timestamp, sources, isError, summary, suggestions } =\n    message;\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString(\"id-ID\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  // Function to format text with basic markdown-like formatting\n  const formatText = (text) => {\n    if (!text) return text;\n\n    // Escape HTML first to prevent XSS\n    let formatted = text\n      .replace(/&/g, \"&amp;\")\n      .replace(/</g, \"&lt;\")\n      .replace(/>/g, \"&gt;\");\n\n    // Convert **bold** to <strong>\n    formatted = formatted.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\");\n\n    // Convert *italic* to <em>\n    formatted = formatted.replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n\n    // Convert line breaks to <br>\n    formatted = formatted.replace(/\\n/g, \"<br>\");\n\n    // Convert numbered lists (1. 2. 3.)\n    formatted = formatted.replace(/^(\\d+\\.\\s)/gm, \"<strong>$1</strong>\");\n\n    // Convert bullet points (- or *)\n    formatted = formatted.replace(/^[-*]\\s/gm, \"• \");\n\n    // Convert ## headers to bold\n    formatted = formatted.replace(/^##\\s(.+)$/gm, \"<strong>$1</strong>\");\n\n    return formatted;\n  };\n\n  return (\n    <div className={`flex ${isBot ? \"justify-start\" : \"justify-end\"}`}>\n      <div\n        className={`max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${\n          isBot\n            ? isError\n              ? \"bg-red-50 dark:bg-red-900 text-red-800 dark:text-red-200 border-2 border-red-300 dark:border-red-600\"\n              : \"bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-700 dark:to-gray-600 text-green-800 dark:text-green-200 border-2 border-green-300 dark:border-gray-500\"\n            : \"bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white shadow-lg\"\n        }`}\n      >\n        <div\n          className=\"text-sm sm:text-base leading-relaxed whitespace-pre-wrap\"\n          dangerouslySetInnerHTML={{ __html: formatText(text) }}\n        />\n\n        {/* Summary Section */}\n        {summary && isBot && (\n          <div className=\"mt-4 p-3 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-600 dark:to-gray-500 rounded-lg border border-green-300 dark:border-gray-400\">\n            <h4 className=\"text-sm font-bold text-green-800 dark:text-green-200 mb-2 flex items-center\">\n              📝 Kesimpulan\n            </h4>\n            <div\n              className=\"text-sm text-green-700 dark:text-green-300 leading-relaxed\"\n              dangerouslySetInnerHTML={{ __html: formatText(summary) }}\n            />\n          </div>\n        )}\n\n        {/* Sources Section with Accordion */}\n        {sources && sources.length > 0 && isBot && (\n          <div className=\"mt-3 pt-3 border-t border-green-400 dark:border-gray-500\">\n            <Accordion type=\"single\" collapsible className=\"w-full\">\n              <AccordionItem value=\"sources\" className=\"border-none\">\n                <AccordionTrigger className=\"text-xs font-semibold text-green-700 dark:text-green-300 hover:no-underline py-2\">\n                  <span className=\"flex items-center gap-2\">\n                    📚 Sumber Referensi ({sources.length})\n                  </span>\n                </AccordionTrigger>\n                <AccordionContent className=\"pt-0 pb-2\">\n                  <ul className=\"text-xs space-y-2 text-green-600 dark:text-green-400\">\n                    {sources.map((source, index) => (\n                      <li\n                        key={index}\n                        className=\"p-2 bg-green-50 dark:bg-gray-700 rounded border border-green-200 dark:border-gray-600\"\n                      >\n                        <span className=\"font-medium text-green-800 dark:text-green-200\">\n                          [{index + 1}]\n                        </span>{\" \"}\n                        {source}\n                      </li>\n                    ))}\n                  </ul>\n                </AccordionContent>\n              </AccordionItem>\n            </Accordion>\n          </div>\n        )}\n\n        <p\n          className={`text-xs mt-2 ${\n            isBot\n              ? \"text-green-600 dark:text-green-400\"\n              : \"text-green-100 dark:text-green-200\"\n          }`}\n        >\n          {formatTime(timestamp)}\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Message;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,gBAAgB,EAChBC,aAAa,EACbC,gBAAgB,QACX,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,OAAO,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC/B,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,SAAS;IAAEC,OAAO;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAY,CAAC,GACtEP,OAAO;EAET,MAAMQ,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIZ,IAAI,IAAK;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAOA,IAAI;;IAEtB;IACA,IAAIa,SAAS,GAAGb,IAAI,CACjBc,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;;IAExB;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;;IAEtE;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC;;IAE1D;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;IAE5C;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,cAAc,EAAE,qBAAqB,CAAC;;IAEpE;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;;IAEhD;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,cAAc,EAAE,qBAAqB,CAAC;IAEpE,OAAOD,SAAS;EAClB,CAAC;EAED,oBACEhB,OAAA;IAAKkB,SAAS,EAAE,QAAQd,KAAK,GAAG,eAAe,GAAG,aAAa,EAAG;IAAAe,QAAA,eAChEnB,OAAA;MACEkB,SAAS,EAAE,2FACTd,KAAK,GACDG,OAAO,GACL,sGAAsG,GACtG,mKAAmK,GACrK,yGAAyG,EAC5G;MAAAY,QAAA,gBAEHnB,OAAA;QACEkB,SAAS,EAAC,0DAA0D;QACpEE,uBAAuB,EAAE;UAAEC,MAAM,EAAEN,UAAU,CAACZ,IAAI;QAAE;MAAE;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EAGDjB,OAAO,IAAIJ,KAAK,iBACfJ,OAAA;QAAKkB,SAAS,EAAC,oJAAoJ;QAAAC,QAAA,gBACjKnB,OAAA;UAAIkB,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAE5F;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzB,OAAA;UACEkB,SAAS,EAAC,4DAA4D;UACtEE,uBAAuB,EAAE;YAAEC,MAAM,EAAEN,UAAU,CAACP,OAAO;UAAE;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAnB,OAAO,IAAIA,OAAO,CAACoB,MAAM,GAAG,CAAC,IAAItB,KAAK,iBACrCJ,OAAA;QAAKkB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEnB,OAAA,CAACL,SAAS;UAACgC,IAAI,EAAC,QAAQ;UAACC,WAAW;UAACV,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrDnB,OAAA,CAACH,aAAa;YAACgC,KAAK,EAAC,SAAS;YAACX,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACpDnB,OAAA,CAACF,gBAAgB;cAACoB,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC5GnB,OAAA;gBAAMkB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GAAC,iCACnB,EAACb,OAAO,CAACoB,MAAM,EAAC,GACvC;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACnBzB,OAAA,CAACJ,gBAAgB;cAACsB,SAAS,EAAC,WAAW;cAAAC,QAAA,eACrCnB,OAAA;gBAAIkB,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EACjEb,OAAO,CAACwB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBhC,OAAA;kBAEEkB,SAAS,EAAC,uFAAuF;kBAAAC,QAAA,gBAEjGnB,OAAA;oBAAMkB,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,GAAC,GAC9D,EAACa,KAAK,GAAG,CAAC,EAAC,GACd;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG,EACVM,MAAM;gBAAA,GANFC,KAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOR,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACN,eAEDzB,OAAA;QACEkB,SAAS,EAAE,gBACTd,KAAK,GACD,oCAAoC,GACpC,oCAAoC,EACvC;QAAAe,QAAA,EAEFT,UAAU,CAACL,SAAS;MAAC;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,EAAA,GAjHIhC,OAAO;AAmHb,eAAeA,OAAO;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}