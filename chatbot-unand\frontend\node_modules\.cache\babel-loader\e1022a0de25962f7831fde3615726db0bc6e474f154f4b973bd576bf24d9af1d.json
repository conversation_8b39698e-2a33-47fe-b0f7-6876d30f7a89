{"ast": null, "code": "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return node => {\n    let hasCleanup = false;\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport { composeRefs, useComposedRefs };", "map": {"version": 3, "names": ["React", "setRef", "ref", "value", "current", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "map", "cleanup", "i", "length", "useComposedRefs", "useCallback"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-compose-refs\\src\\compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AAQvB,SAASC,OAAUC,GAAA,EAAqBC,KAAA,EAAU;EAChD,IAAI,OAAOD,GAAA,KAAQ,YAAY;IAC7B,OAAOA,GAAA,CAAIC,KAAK;EAClB,WAAWD,GAAA,KAAQ,QAAQA,GAAA,KAAQ,QAAW;IAC5CA,GAAA,CAAIE,OAAA,GAAUD,KAAA;EAChB;AACF;AAMA,SAASE,YAAA,GAAkBC,IAAA,EAA8C;EACvE,OAAQC,IAAA,IAAS;IACf,IAAIC,UAAA,GAAa;IACjB,MAAMC,QAAA,GAAWH,IAAA,CAAKI,GAAA,CAAKR,GAAA,IAAQ;MACjC,MAAMS,OAAA,GAAUV,MAAA,CAAOC,GAAA,EAAKK,IAAI;MAChC,IAAI,CAACC,UAAA,IAAc,OAAOG,OAAA,IAAW,YAAY;QAC/CH,UAAA,GAAa;MACf;MACA,OAAOG,OAAA;IACT,CAAC;IAMD,IAAIH,UAAA,EAAY;MACd,OAAO,MAAM;QACX,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIH,QAAA,CAASI,MAAA,EAAQD,CAAA,IAAK;UACxC,MAAMD,OAAA,GAAUF,QAAA,CAASG,CAAC;UAC1B,IAAI,OAAOD,OAAA,IAAW,YAAY;YAChCA,OAAA,CAAQ;UACV,OAAO;YACLV,MAAA,CAAOK,IAAA,CAAKM,CAAC,GAAG,IAAI;UACtB;QACF;MACF;IACF;EACF;AACF;AAMA,SAASE,gBAAA,GAAsBR,IAAA,EAA8C;EAE3E,OAAaN,KAAA,CAAAe,WAAA,CAAYV,WAAA,CAAY,GAAGC,IAAI,GAAGA,IAAI;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}