{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\ChatInput.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInput = ({\n  onSendMessage,\n  isLoading,\n  onFileUpload\n}) => {\n  _s();\n  const [message, setMessage] = useState(\"\");\n  const fileInputRef = useRef(null);\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n  const handleFileUpload = event => {\n    if (onFileUpload) {\n      onFileUpload(event);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"border-t-2 border-green-600 dark:border-green-400 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-800 dark:to-gray-700\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center p-3 sm:p-4 gap-2 sm:gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-upload\",\n          className: \"bg-gradient-to-r from-yellow-600 to-yellow-700 dark:from-yellow-500 dark:to-yellow-600 hover:from-yellow-700 hover:to-yellow-800 dark:hover:from-yellow-600 dark:hover:to-yellow-700 text-white font-medium py-2 px-3 sm:py-3 sm:px-4 rounded-full text-sm cursor-pointer disabled:opacity-50 flex items-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 h-10 sm:h-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hidden sm:inline\",\n            children: \"Unggah\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-upload\",\n          type: \"file\",\n          ref: fileInputRef,\n          className: \"hidden\",\n          onChange: handleFileUpload,\n          disabled: isLoading,\n          accept: \".docx\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"flex flex-1 items-center gap-2 sm:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"flex-grow rounded-full py-2 px-3 sm:py-3 sm:px-4 bg-white border-2 border-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 shadow-md h-10 sm:h-12 text-sm sm:text-base\",\n          placeholder: \"Tulis pesan Anda...\",\n          value: message,\n          onChange: e => setMessage(e.target.value),\n          disabled: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"bg-gradient-to-r from-green-600 to-green-700 text-white rounded-full p-2 sm:p-3 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 shadow-md hover:shadow-lg transition-all duration-200 h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center flex-shrink-0\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"animate-spin h-4 w-4 sm:h-5 sm:w-5 text-white\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              className: \"opacity-25\",\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              stroke: \"currentColor\",\n              strokeWidth: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              className: \"opacity-75\",\n              fill: \"currentColor\",\n              d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4 sm:h-5 sm:w-5\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInput, \"NsWI/hgCQtIQrekRItai4l5FBnw=\");\n_c = ChatInput;\nexport default ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "jsxDEV", "_jsxDEV", "ChatInput", "onSendMessage", "isLoading", "onFileUpload", "_s", "message", "setMessage", "fileInputRef", "handleSubmit", "e", "preventDefault", "trim", "handleFileUpload", "event", "className", "children", "htmlFor", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "type", "ref", "onChange", "disabled", "accept", "onSubmit", "placeholder", "value", "target", "xmlns", "cx", "cy", "r", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/ChatInput.jsx"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\n\nconst ChatInput = ({ onSendMessage, isLoading, onFileUpload }) => {\n  const [message, setMessage] = useState(\"\");\n  const fileInputRef = useRef(null);\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n\n  const handleFileUpload = (event) => {\n    if (onFileUpload) {\n      onFileUpload(event);\n    }\n  };\n\n  return (\n    <div className=\"border-t-2 border-green-600 dark:border-green-400 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-800 dark:to-gray-700\">\n      <div className=\"flex items-center p-3 sm:p-4 gap-2 sm:gap-3\">\n        {/* Upload Button - Left Side */}\n        <div className=\"flex-shrink-0\">\n          <label\n            htmlFor=\"file-upload\"\n            className=\"bg-gradient-to-r from-yellow-600 to-yellow-700 dark:from-yellow-500 dark:to-yellow-600 hover:from-yellow-700 hover:to-yellow-800 dark:hover:from-yellow-600 dark:hover:to-yellow-700 text-white font-medium py-2 px-3 sm:py-3 sm:px-4 rounded-full text-sm cursor-pointer disabled:opacity-50 flex items-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 h-10 sm:h-12\"\n          >\n            <svg\n              className=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n              />\n            </svg>\n            <span className=\"hidden sm:inline\">Unggah</span>\n          </label>\n          <input\n            id=\"file-upload\"\n            type=\"file\"\n            ref={fileInputRef}\n            className=\"hidden\"\n            onChange={handleFileUpload}\n            disabled={isLoading}\n            accept=\".docx\"\n          />\n        </div>\n\n        {/* Message Input Form */}\n        <form\n          onSubmit={handleSubmit}\n          className=\"flex flex-1 items-center gap-2 sm:gap-3\"\n        >\n          <input\n            type=\"text\"\n            className=\"flex-grow rounded-full py-2 px-3 sm:py-3 sm:px-4 bg-white border-2 border-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 shadow-md h-10 sm:h-12 text-sm sm:text-base\"\n            placeholder=\"Tulis pesan Anda...\"\n            value={message}\n            onChange={(e) => setMessage(e.target.value)}\n            disabled={isLoading}\n          />\n          <button\n            type=\"submit\"\n            className=\"bg-gradient-to-r from-green-600 to-green-700 text-white rounded-full p-2 sm:p-3 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 shadow-md hover:shadow-lg transition-all duration-200 h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center flex-shrink-0\"\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <svg\n                className=\"animate-spin h-4 w-4 sm:h-5 sm:w-5 text-white\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n              >\n                <circle\n                  className=\"opacity-25\"\n                  cx=\"12\"\n                  cy=\"12\"\n                  r=\"10\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"4\"\n                ></circle>\n                <path\n                  className=\"opacity-75\"\n                  fill=\"currentColor\"\n                  d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                ></path>\n              </svg>\n            ) : (\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-4 w-4 sm:h-5 sm:w-5\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path d=\"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\" />\n              </svg>\n            )}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatInput;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,SAAS,GAAGA,CAAC;EAAEC,aAAa;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMW,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMW,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIL,OAAO,CAACM,IAAI,CAAC,CAAC,IAAI,CAACT,SAAS,EAAE;MAChCD,aAAa,CAACI,OAAO,CAAC;MACtBC,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAIC,KAAK,IAAK;IAClC,IAAIV,YAAY,EAAE;MAChBA,YAAY,CAACU,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,qIAAqI;IAAAC,QAAA,eAClJhB,OAAA;MAAKe,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DhB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhB,OAAA;UACEiB,OAAO,EAAC,aAAa;UACrBF,SAAS,EAAC,0XAA0X;UAAAC,QAAA,gBAEpYhB,OAAA;YACEe,SAAS,EAAC,SAAS;YACnBG,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAEnBhB,OAAA;cACEqB,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAuF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5B,OAAA;YAAMe,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACR5B,OAAA;UACE6B,EAAE,EAAC,aAAa;UAChBC,IAAI,EAAC,MAAM;UACXC,GAAG,EAAEvB,YAAa;UAClBO,SAAS,EAAC,QAAQ;UAClBiB,QAAQ,EAAEnB,gBAAiB;UAC3BoB,QAAQ,EAAE9B,SAAU;UACpB+B,MAAM,EAAC;QAAO;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5B,OAAA;QACEmC,QAAQ,EAAE1B,YAAa;QACvBM,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEnDhB,OAAA;UACE8B,IAAI,EAAC,MAAM;UACXf,SAAS,EAAC,6MAA6M;UACvNqB,WAAW,EAAC,qBAAqB;UACjCC,KAAK,EAAE/B,OAAQ;UACf0B,QAAQ,EAAGtB,CAAC,IAAKH,UAAU,CAACG,CAAC,CAAC4B,MAAM,CAACD,KAAK,CAAE;UAC5CJ,QAAQ,EAAE9B;QAAU;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACF5B,OAAA;UACE8B,IAAI,EAAC,QAAQ;UACbf,SAAS,EAAC,iUAAiU;UAC3UkB,QAAQ,EAAE9B,SAAU;UAAAa,QAAA,EAEnBb,SAAS,gBACRH,OAAA;YACEe,SAAS,EAAC,+CAA+C;YACzDwB,KAAK,EAAC,4BAA4B;YAClCrB,IAAI,EAAC,MAAM;YACXE,OAAO,EAAC,WAAW;YAAAJ,QAAA,gBAEnBhB,OAAA;cACEe,SAAS,EAAC,YAAY;cACtByB,EAAE,EAAC,IAAI;cACPC,EAAE,EAAC,IAAI;cACPC,CAAC,EAAC,IAAI;cACNvB,MAAM,EAAC,cAAc;cACrBI,WAAW,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACV5B,OAAA;cACEe,SAAS,EAAC,YAAY;cACtBG,IAAI,EAAC,cAAc;cACnBM,CAAC,EAAC;YAAiH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,gBAEN5B,OAAA;YACEuC,KAAK,EAAC,4BAA4B;YAClCxB,SAAS,EAAC,uBAAuB;YACjCK,OAAO,EAAC,WAAW;YACnBF,IAAI,EAAC,cAAc;YAAAF,QAAA,eAEnBhB,OAAA;cAAMwB,CAAC,EAAC;YAAsL;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9L;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA3GIJ,SAAS;AAAA0C,EAAA,GAAT1C,SAAS;AA6Gf,eAAeA,SAAS;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}