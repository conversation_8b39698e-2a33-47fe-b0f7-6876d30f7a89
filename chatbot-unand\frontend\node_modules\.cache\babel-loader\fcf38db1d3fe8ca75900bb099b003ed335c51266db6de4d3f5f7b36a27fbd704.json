{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 11H4\",\n  key: \"6ut86h\"\n}], [\"path\", {\n  d: \"M20 7H4\",\n  key: \"zbl0bi\"\n}], [\"path\", {\n  d: \"M7 21V4a1 1 0 0 1 1-1h4a1 1 0 0 1 0 12H7\",\n  key: \"1ana5r\"\n}]];\nconst PhilippinePeso = createLucideIcon(\"philippine-peso\", __iconNode);\nexport { __iconNode, PhilippinePeso as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "PhilippinePeso", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\philippine-peso.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M20 11H4', key: '6ut86h' }],\n  ['path', { d: 'M20 7H4', key: 'zbl0bi' }],\n  ['path', { d: 'M7 21V4a1 1 0 0 1 1-1h4a1 1 0 0 1 0 12H7', key: '1ana5r' }],\n];\n\n/**\n * @component @name PhilippinePeso\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTFINCIgLz4KICA8cGF0aCBkPSJNMjAgN0g0IiAvPgogIDxwYXRoIGQ9Ik03IDIxVjRhMSAxIDAgMCAxIDEtMWg0YTEgMSAwIDAgMSAwIDEySDciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/philippine-peso\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PhilippinePeso = createLucideIcon('philippine-peso', __iconNode);\n\nexport default PhilippinePeso;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAU,GAC3E;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}