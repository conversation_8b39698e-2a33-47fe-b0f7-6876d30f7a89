{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 18h8\",\n  key: \"1borvv\"\n}], [\"path\", {\n  d: \"M3 22h18\",\n  key: \"8prr45\"\n}], [\"path\", {\n  d: \"M14 22a7 7 0 1 0 0-14h-1\",\n  key: \"1jwaiy\"\n}], [\"path\", {\n  d: \"M9 14h2\",\n  key: \"197e7h\"\n}], [\"path\", {\n  d: \"M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z\",\n  key: \"1bmzmy\"\n}], [\"path\", {\n  d: \"M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3\",\n  key: \"1drr47\"\n}]];\nconst Microscope = createLucideIcon(\"microscope\", __iconNode);\nexport { __iconNode, Microscope as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Microscope", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\microscope.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 18h8', key: '1borvv' }],\n  ['path', { d: 'M3 22h18', key: '8prr45' }],\n  ['path', { d: 'M14 22a7 7 0 1 0 0-14h-1', key: '1jwaiy' }],\n  ['path', { d: 'M9 14h2', key: '197e7h' }],\n  ['path', { d: 'M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z', key: '1bmzmy' }],\n  ['path', { d: 'M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3', key: '1drr47' }],\n];\n\n/**\n * @component @name Microscope\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAxOGg4IiAvPgogIDxwYXRoIGQ9Ik0zIDIyaDE4IiAvPgogIDxwYXRoIGQ9Ik0xNCAyMmE3IDcgMCAxIDAgMC0xNGgtMSIgLz4KICA8cGF0aCBkPSJNOSAxNGgyIiAvPgogIDxwYXRoIGQ9Ik05IDEyYTIgMiAwIDAgMS0yLTJWNmg2djRhMiAyIDAgMCAxLTIgMloiIC8+CiAgPHBhdGggZD0iTTEyIDZWM2ExIDEgMCAwIDAtMS0xSDlhMSAxIDAgMCAwLTEgMXYzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/microscope\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Microscope = createLucideIcon('microscope', __iconNode);\n\nexport default Microscope;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAU,GAC1E;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,eAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}