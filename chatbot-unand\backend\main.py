import os
import json
from dotenv import load_dotenv
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import google.generativeai as genai
from docx import Document
import faiss
import numpy as np
import shutil
from typing import List, Dict, Optional
import traceback # Import untuk logging traceback
from fastapi.responses import PlainTextResponse # Import ini untuk handle OPTIONS
from sqlalchemy.orm import Session
from database import get_db, create_tables, ChatSession, ChatMessage
from chat_service import ChatService

# Load environment variables from .env file in the project root
# Pastikan jalur ke .env benar, ini asumsi .env ada di satu level di atas folder backend
load_dotenv(dotenv_path=os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY not found in .env file. Please check your .env file in the project root.")

# Configure Gemini API
genai.configure(api_key=GEMINI_API_KEY)

# --- Configuration for Embeddings and Generative Model ---
EMBEDDING_MODEL = "models/embedding-001"  # Model embedding yang stabil dan tersedia
GENERATIVE_MODEL = "gemini-1.5-flash"     # Model generative yang stabil dan tersedia
# --- FAISS Index and Document Chunks Paths ---
# Jalur relatif ke folder backend
VECTOR_DB_DIR = os.path.join(os.path.dirname(__file__), "vector_db")
FAISS_INDEX_PATH = os.path.join(VECTOR_DB_DIR, "faiss_index.bin")
DOC_CHUNKS_PATH = os.path.join(VECTOR_DB_DIR, "doc_chunks.json")
DATA_DIR = os.path.join(os.path.dirname(__file__), "data")

faiss_index = None
doc_chunks = []
generative_model = None

# --- Helper Functions for Document Processing ---
def load_faiss_index_and_chunks():
    """Loads FAISS index and document chunks if they exist."""
    global faiss_index, doc_chunks
    if os.path.exists(FAISS_INDEX_PATH) and os.path.exists(DOC_CHUNKS_PATH):
        try:
            print("Loading FAISS index and document chunks...")
            faiss_index = faiss.read_index(FAISS_INDEX_PATH)
            with open(DOC_CHUNKS_PATH, 'r', encoding='utf-8') as f:
                doc_chunks = json.load(f)
            print("Loaded FAISS index and document chunks successfully.")
        except Exception as e:
            print(f"Error loading FAISS index or chunks: {e}. Will attempt to re-process.")
            faiss_index = None
            doc_chunks = []
    else:
        print("FAISS index or document chunks not found. Will run pre-processing.")
        faiss_index = None
        doc_chunks = []

def extract_text_from_docx(filepath):
    """Extracts text from a .docx file."""
    doc = Document(filepath)
    full_text = []
    for para in doc.paragraphs:
        if para.text.strip():
            full_text.append(para.text.strip())
    return "\n".join(full_text)

def chunk_text(text, max_chars=1000, overlap=100):
    """Chunks text into smaller pieces with optional overlap."""
    chunks = []
    start = 0
    while start < len(text):
        end = min(start + max_chars, len(text))
        chunk = text[start:end]
        chunks.append(chunk)
        if end == len(text):
            break
        start += (max_chars - overlap)
    return chunks

def generate_embeddings_batch(texts: List[str], task_type="RETRIEVAL_DOCUMENT") -> List[List[float]]:
    """Generates embeddings for a list of texts in batches."""
    embeddings = []
    batch_size = 50 # Adjust based on API rate limits and memory
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]
        try:
            # genai.embed_content is synchronous, not async
            results = genai.embed_content(model=EMBEDDING_MODEL, content=batch_texts, task_type=task_type)
            embeddings.extend(results['embedding'])
        except Exception as e:
            print(f"Error generating embeddings for batch (index {i}): {e}")
            traceback.print_exc() # Print full traceback for debugging
            # Jika terjadi error, kembalikan embedding dummy agar proses tidak berhenti total
            embeddings.extend([np.zeros(768).tolist()] * len(batch_texts))
    return embeddings

async def preprocess_documents_and_build_index():
    """
    Asynchronous function to extract text, chunk, embed, and build/update FAISS index.
    Run this whenever your regulation documents change.
    """
    global faiss_index, doc_chunks
    print("Starting document pre-processing...")

    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(VECTOR_DB_DIR, exist_ok=True)

    all_texts = []
    for filename in os.listdir(DATA_DIR):
        if filename.endswith(".docx") and not filename.startswith("~$"):
            filepath = os.path.join(DATA_DIR, filename)
            print(f"Processing {filepath}...")
            try:
                text = extract_text_from_docx(filepath)
                all_texts.extend(chunk_text(text))
            except Exception as e:
                print(f"Error processing {filepath}: {e}")
                continue

    if not all_texts:
        print("No text extracted from documents. Check your .docx files in backend/data.")
        faiss_index = None
        doc_chunks = []
        # Remove old index/chunks if no documents are found, to avoid stale data
        if os.path.exists(FAISS_INDEX_PATH): os.remove(FAISS_INDEX_PATH)
        if os.path.exists(DOC_CHUNKS_PATH): os.remove(DOC_CHUNKS_PATH)
        return

    print(f"Generated {len(all_texts)} text chunks.")
    doc_chunks = all_texts # Update global doc_chunks with new/all chunks

    print("Generating embeddings for document chunks (this might take a while)...")
    embeddings = generate_embeddings_batch(doc_chunks, task_type="RETRIEVAL_DOCUMENT")

    if not embeddings or len(embeddings) != len(doc_chunks):
        print("Failed to generate embeddings for all chunks. Aborting FAISS index creation or re-creation.")
        faiss_index = None
        doc_chunks = [] # Clear chunks if embeddings failed
        # Clean up corrupted/incomplete index files if any
        if os.path.exists(FAISS_INDEX_PATH): os.remove(FAISS_INDEX_PATH)
        if os.path.exists(DOC_CHUNKS_PATH): os.remove(DOC_CHUNKS_PATH)
        return

    embeddings_np = np.array(embeddings).astype('float32')
    dimension = embeddings_np.shape[1]

    # Create and populate FAISS index
    faiss_index = faiss.IndexFlatL2(dimension) # L2 for Euclidean distance, suitable for embeddings
    faiss_index.add(embeddings_np)

    # Save FAISS index and chunks
    faiss.write_index(faiss_index, FAISS_INDEX_PATH)
    with open(DOC_CHUNKS_PATH, 'w', encoding='utf-8') as f:
        json.dump(doc_chunks, f, ensure_ascii=False, indent=2)

    print("Document pre-processing complete. FAISS index created/updated and saved.")

# --- FastAPI App Initialization ---
app = FastAPI()

# Allow CORS for frontend
origins = [
    "http://localhost:3001",  # React default port. SESUAIKAN JIKA FRONTEND BERJALAN DI PORT LAIN!
    "http://localhost:3000",  # Tambahkan ini sebagai fallback jika React kadang menggunakan 3000
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"], # Izinkan semua metode (termasuk OPTIONS)
    allow_headers=["*"], # Izinkan semua header
)

@app.on_event("startup")
async def startup_event():
    global generative_model
    generative_model = genai.GenerativeModel(GENERATIVE_MODEL)
    print("Gemini generative model loaded.")

    # Initialize database tables
    create_tables()
    print("Database tables created/verified.")

    # Force rebuild index with new parameters
    print("Rebuilding index with improved parameters...")
    await preprocess_documents_and_build_index()
    load_faiss_index_and_chunks()


# Pydantic models for API
class ChatRequest(BaseModel):
    query: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    sources: List[str] = []
    summary: Optional[str] = None

class SessionRequest(BaseModel):
    title: Optional[str] = None

class SessionResponse(BaseModel):
    session_id: str
    title: str
    created_at: str
    updated_at: str

class MessageResponse(BaseModel):
    id: int
    message_type: str
    content: str
    timestamp: str
    sources: Optional[List[str]] = None
    summary: Optional[str] = None

# Handle CORS preflight for /chat explicitly
@app.options("/chat")
async def options_chat():
    """Handle CORS preflight for /chat endpoint."""
    return PlainTextResponse(status_code=200)

@app.post("/chat", response_model=ChatResponse)
async def chat_with_rag(request: ChatRequest, db: Session = Depends(get_db)):
    if faiss_index is None or not doc_chunks:
        # Memberikan pesan yang lebih informatif jika indeks belum siap
        raise HTTPException(status_code=500, detail="Sistem belum siap. Indeks dokumen sedang dibuat atau belum ada dokumen yang diunggah. Mohon tunggu atau unggah dokumen.")

    query_text = request.query
    chat_service = ChatService(db)

    # Handle session
    session_id = request.session_id
    if not session_id:
        # Create new session with title from first message
        title = chat_service.generate_session_title(query_text)
        session_id = chat_service.create_session(title)

    # Save user message
    chat_service.add_message(session_id, "user", query_text)

    try:
        # 1. Generate embedding for the query
        # genai.embed_content is synchronous, not async
        query_embedding_response = genai.embed_content(model=EMBEDDING_MODEL, content=query_text, task_type="RETRIEVAL_QUERY")
        
        # Validasi output dari genai.embed_content
        if not isinstance(query_embedding_response, dict) or 'embedding' not in query_embedding_response:
            raise ValueError(f"Unexpected response format from embedding API: {query_embedding_response}")

        query_embedding = np.array(query_embedding_response['embedding']).astype('float32').reshape(1, -1)

        # 2. Search FAISS index for relevant chunks
        k = 5 # Number of top relevant chunks to retrieve
        distances, indices = faiss_index.search(query_embedding, k)
        
        # Filter out invalid indices and get relevant chunks
        # Pastikan indeks yang diambil valid dan tidak melebihi batas doc_chunks
        relevant_chunks = [doc_chunks[i] for i in indices[0] if 0 <= i < len(doc_chunks)]

        # Debug info
        print(f"Query: {query_text}")
        print(f"Found {len(relevant_chunks)} relevant chunks")
        for i, chunk in enumerate(relevant_chunks):
            print(f"Chunk {i+1}: {chunk[:100]}...")

        if not relevant_chunks:
            return {"response": "Maaf, saya tidak menemukan informasi relevan dalam dokumen peraturan yang ada untuk pertanyaan Anda."}

        # 3. Construct prompt for Gemini (RAG approach)
        context = "\n\n".join(relevant_chunks)

        # Create sources list from relevant chunks
        sources = [f"Dokumen {i+1}: {chunk[:100]}..." for i, chunk in enumerate(relevant_chunks)]

        prompt = (
            "Anda adalah asisten AI yang cerdas dan informatif untuk Universitas Andalas. "
            "Tugas Anda adalah memberikan jawaban yang komprehensif, terstruktur, dan mudah dibaca.\n\n"

            "INSTRUKSI FORMAT JAWABAN:\n"
            "1. Gunakan format yang rapi dengan heading, bullet points, dan numbering\n"
            "2. Pisahkan informasi menjadi bagian-bagian yang logis\n"
            "3. Gunakan **bold** untuk poin penting dan *italic* untuk penekanan\n"
            "4. Sertakan referensi peraturan (pasal, ayat, nomor) dengan jelas\n"
            "5. Berikan jawaban lengkap dan detail\n"
            "6. Gunakan bahasa Indonesia yang formal namun mudah dipahami\n\n"

            "STRUKTUR JAWABAN:\n"
            "- Mulai dengan penjelasan utama\n"
            "- Buat poin-poin terorganisir dengan baik\n"
            "- Sertakan detail spesifik dari peraturan\n"
            "- Jawaban harus lengkap dan informatif\n\n"

            "KONTEKS PERATURAN:\n" + context + "\n\n"

            "PERTANYAAN: " + query_text + "\n\n"

            "JAWABAN TERSTRUKTUR:"
        )

        # 4. Generate response using Gemini
        response = generative_model.generate_content(prompt)
        # Handle cases where Gemini might return no text or blocked content
        if not response.text:
            bot_response = "Maaf, saya tidak dapat menghasilkan jawaban yang relevan saat ini. Mungkin pertanyaan Anda sensitif atau tidak sesuai dengan peraturan yang ada."
        else:
            bot_response = response.text

        # 5. Generate summary using a separate prompt
        summary_prompt = (
            "Berdasarkan jawaban berikut, buatlah kesimpulan singkat dalam 2-3 kalimat yang merangkum poin utama:\n\n"
            f"JAWABAN: {bot_response}\n\n"
            "KESIMPULAN:"
        )

        summary_response = generative_model.generate_content(summary_prompt)
        summary = summary_response.text if summary_response.text else None

        # Save bot response
        chat_service.add_message(session_id, "bot", bot_response, sources, summary)

        return ChatResponse(
            response=bot_response,
            session_id=session_id,
            sources=sources,
            summary=summary
        )

    except Exception as e:
        print(f"Error during chat processing for query '{query_text}': {e}")
        # Log the full traceback for debugging in development
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Terjadi kesalahan internal saat memproses pertanyaan Anda: {e}. Mohon coba lagi.")

# Session management endpoints
@app.post("/sessions", response_model=SessionResponse)
async def create_session(request: SessionRequest, db: Session = Depends(get_db)):
    """Create a new chat session"""
    chat_service = ChatService(db)
    session_id = chat_service.create_session(request.title or "New Chat")
    session = chat_service.get_session(session_id)

    return SessionResponse(
        session_id=session.session_id,
        title=session.title,
        created_at=session.created_at.isoformat(),
        updated_at=session.updated_at.isoformat()
    )

@app.get("/sessions", response_model=List[SessionResponse])
async def get_sessions(db: Session = Depends(get_db)):
    """Get all chat sessions"""
    chat_service = ChatService(db)
    sessions = chat_service.get_all_sessions()

    return [
        SessionResponse(
            session_id=session.session_id,
            title=session.title,
            created_at=session.created_at.isoformat(),
            updated_at=session.updated_at.isoformat()
        )
        for session in sessions
    ]

@app.get("/sessions/{session_id}/messages", response_model=List[MessageResponse])
async def get_session_messages(session_id: str, db: Session = Depends(get_db)):
    """Get all messages for a session"""
    chat_service = ChatService(db)
    messages = chat_service.get_messages(session_id)

    return [
        MessageResponse(
            id=msg.id,
            message_type=msg.message_type,
            content=msg.content,
            timestamp=msg.timestamp.isoformat(),
            sources=json.loads(msg.sources) if msg.sources else None,
            summary=msg.summary
        )
        for msg in messages
    ]

@app.put("/sessions/{session_id}")
async def update_session(session_id: str, request: SessionRequest, db: Session = Depends(get_db)):
    """Update session title"""
    chat_service = ChatService(db)
    success = chat_service.update_session_title(session_id, request.title)

    if not success:
        raise HTTPException(status_code=404, detail="Session not found")

    return {"message": "Session updated successfully"}

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str, db: Session = Depends(get_db)):
    """Delete a session"""
    chat_service = ChatService(db)
    success = chat_service.delete_session(session_id)

    if not success:
        raise HTTPException(status_code=404, detail="Session not found")

    return {"message": "Session deleted successfully"}

# Handle CORS preflight for /upload-document explicitly
@app.options("/upload-document")
async def options_upload_document():
    """Handle CORS preflight for /upload-document endpoint."""
    return PlainTextResponse(status_code=200)

@app.post("/upload-document")
async def upload_document(file: UploadFile = File(...)):
    """
    Handles uploaded .docx files, saves them, and triggers re-preprocessing.
    """
    if not file.filename.endswith(".docx"):
        raise HTTPException(status_code=400, detail="Hanya file .docx yang diizinkan.")

    file_location = os.path.join(DATA_DIR, file.filename)
    try:
        # Ensure the data directory exists
        os.makedirs(DATA_DIR, exist_ok=True)
        # Check if file already exists to avoid overwriting without warning (optional)
        if os.path.exists(file_location):
            print(f"Warning: Overwriting existing file '{file.filename}'")
            # You might want to handle this differently, e.g., rename or ask for confirmation
        
        with open(file_location, "wb+") as file_object:
            shutil.copyfileobj(file.file, file_object)
    except Exception as e:
        print(f"Error saving file '{file.filename}': {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Gagal menyimpan file: {e}. Error: {str(e)}")

    print(f"File {file.filename} diupload. Memulai pra-pemrosesan ulang sistem chatbot...")
    try:
        await preprocess_documents_and_build_index() # Panggil fungsi pra-pemrosesan secara async
        load_faiss_index_and_chunks() # Pastikan indeks dimuat ulang setelah preprocessing
        return {"message": f"File '{file.filename}' berhasil diunggah dan sistem chatbot telah diperbarui."}
    except Exception as e:
        print(f"Error during post-upload preprocessing for file '{file.filename}': {e}")
        traceback.print_exc()
        # Jika pra-pemrosesan gagal setelah upload, berikan pesan yang jelas
        raise HTTPException(status_code=500, detail=f"File berhasil diunggah, namun gagal memperbarui sistem chatbot: {e}. Data mungkin tidak terindeks. Mohon coba lagi atau hubungi administrator.")

@app.get("/health")
async def health_check():
    """Endpoint to check if the server is running and index is loaded."""
    status = "OK" if faiss_index is not None and len(doc_chunks) > 0 else "Indexing_In_Progress_or_Failed"
    chunk_count_val = len(doc_chunks) if doc_chunks else 0
    return {"status": status, "index_loaded": faiss_index is not None, "chunk_count": chunk_count_val}