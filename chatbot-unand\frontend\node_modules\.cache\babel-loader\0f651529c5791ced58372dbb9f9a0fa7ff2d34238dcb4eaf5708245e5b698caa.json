{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 4.1 12 6\",\n  key: \"ita8i4\"\n}], [\"path\", {\n  d: \"m5.1 8-2.9-.8\",\n  key: \"1go3kf\"\n}], [\"path\", {\n  d: \"m6 12-1.9 2\",\n  key: \"mnht97\"\n}], [\"path\", {\n  d: \"M7.2 2.2 8 5.1\",\n  key: \"1cfko1\"\n}], [\"path\", {\n  d: \"M9.037 9.69a.498.498 0 0 1 .653-.653l11 4.5a.5.5 0 0 1-.074.949l-4.349 1.041a1 1 0 0 0-.74.739l-1.04 4.35a.5.5 0 0 1-.95.074z\",\n  key: \"s0h3yz\"\n}]];\nconst MousePointerClick = createLucideIcon(\"mouse-pointer-click\", __iconNode);\nexport { __iconNode, MousePointerClick as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MousePointerClick", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\mouse-pointer-click.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 4.1 12 6', key: 'ita8i4' }],\n  ['path', { d: 'm5.1 8-2.9-.8', key: '1go3kf' }],\n  ['path', { d: 'm6 12-1.9 2', key: 'mnht97' }],\n  ['path', { d: 'M7.2 2.2 8 5.1', key: '1cfko1' }],\n  [\n    'path',\n    {\n      d: 'M9.037 9.69a.498.498 0 0 1 .653-.653l11 4.5a.5.5 0 0 1-.074.949l-4.349 1.041a1 1 0 0 0-.74.739l-1.04 4.35a.5.5 0 0 1-.95.074z',\n      key: 's0h3yz',\n    },\n  ],\n];\n\n/**\n * @component @name MousePointerClick\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgNC4xIDEyIDYiIC8+CiAgPHBhdGggZD0ibTUuMSA4LTIuOS0uOCIgLz4KICA8cGF0aCBkPSJtNiAxMi0xLjkgMiIgLz4KICA8cGF0aCBkPSJNNy4yIDIuMiA4IDUuMSIgLz4KICA8cGF0aCBkPSJNOS4wMzcgOS42OWEuNDk4LjQ5OCAwIDAgMSAuNjUzLS42NTNsMTEgNC41YS41LjUgMCAwIDEtLjA3NC45NDlsLTQuMzQ5IDEuMDQxYTEgMSAwIDAgMC0uNzQuNzM5bC0xLjA0IDQuMzVhLjUuNSAwIDAgMS0uOTUuMDc0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mouse-pointer-click\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MousePointerClick = createLucideIcon('mouse-pointer-click', __iconNode);\n\nexport default MousePointerClick;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,iBAAA,GAAoBC,gBAAiB,wBAAuBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}