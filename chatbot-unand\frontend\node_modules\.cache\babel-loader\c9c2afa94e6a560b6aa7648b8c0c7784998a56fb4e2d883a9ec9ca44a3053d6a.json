{"ast": null, "code": "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, {\n  checkForDefaultPrevented = true\n} = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport { composeEventHandlers };", "map": {"version": 3, "names": ["composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "handleEvent", "event", "defaultPrevented"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\primitive\\src\\primitive.tsx"], "sourcesContent": ["function composeEventHandlers<E extends { defaultPrevented: boolean }>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n"], "mappings": ";AAAA,SAASA,qBACPC,oBAAA,EACAC,eAAA,EACA;EAAEC,wBAAA,GAA2B;AAAK,IAAI,CAAC,GACvC;EACA,OAAO,SAASC,YAAYC,KAAA,EAAU;IACpCJ,oBAAA,GAAuBI,KAAK;IAE5B,IAAIF,wBAAA,KAA6B,SAAS,CAACE,KAAA,CAAMC,gBAAA,EAAkB;MACjE,OAAOJ,eAAA,GAAkBG,KAAK;IAChC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}