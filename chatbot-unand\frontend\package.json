{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-switch": "^1.2.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scripts": "5.0.1", "tailwind-merge": "^3.3.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.4"}}