import React, { useState, useRef } from "react";

const ChatInput = ({ onSendMessage, isLoading, onFileUpload }) => {
  const [message, setMessage] = useState("");
  const fileInputRef = useRef(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message);
      setMessage("");
    }
  };

  const handleFileUpload = (event) => {
    if (onFileUpload) {
      onFileUpload(event);
    }
  };

  return (
    <div className="border-t-2 border-green-600 dark:border-green-400 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-800 dark:to-gray-700">
      <div className="flex items-center p-3 sm:p-4 gap-2 sm:gap-3">
        {/* Upload Button - Left Side */}
        <div className="flex-shrink-0">
          <label
            htmlFor="file-upload"
            className="bg-gradient-to-r from-yellow-600 to-yellow-700 dark:from-yellow-500 dark:to-yellow-600 hover:from-yellow-700 hover:to-yellow-800 dark:hover:from-yellow-600 dark:hover:to-yellow-700 text-white font-medium py-2 px-3 sm:py-3 sm:px-4 rounded-full text-sm cursor-pointer disabled:opacity-50 flex items-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 h-10 sm:h-12"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
            <span className="hidden sm:inline">Unggah</span>
          </label>
          <input
            id="file-upload"
            type="file"
            ref={fileInputRef}
            className="hidden"
            onChange={handleFileUpload}
            disabled={isLoading}
            accept=".docx"
          />
        </div>

        {/* Message Input Form */}
        <form
          onSubmit={handleSubmit}
          className="flex flex-1 items-center gap-2 sm:gap-3"
        >
          <input
            type="text"
            className="flex-grow rounded-full py-2 px-3 sm:py-3 sm:px-4 bg-white dark:bg-gray-700 border-2 border-green-300 dark:border-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 shadow-md h-10 sm:h-12 text-sm sm:text-base text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-300"
            placeholder="Tulis pesan Anda..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            disabled={isLoading}
          />
          <button
            type="submit"
            className="bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white rounded-full p-2 sm:p-3 hover:from-green-700 hover:to-green-800 dark:hover:from-green-600 dark:hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 disabled:opacity-50 shadow-md hover:shadow-lg transition-all duration-200 h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center flex-shrink-0"
            disabled={isLoading}
          >
            {isLoading ? (
              <svg
                className="animate-spin h-4 w-4 sm:h-5 sm:w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 sm:h-5 sm:w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z" />
              </svg>
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatInput;
