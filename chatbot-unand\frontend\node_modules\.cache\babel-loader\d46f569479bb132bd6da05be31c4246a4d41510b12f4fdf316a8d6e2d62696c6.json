{"ast": null, "code": "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId(reactId => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport { useId };", "map": {"version": 3, "names": ["React", "useLayoutEffect", "useReactId", "trim", "toString", "count", "useId", "deterministicId", "id", "setId", "useState", "reactId", "String"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-id\\src\\id.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We spaces with `.trim().toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)[' useId '.trim().toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AACvB,SAASC,eAAA,QAAuB;AAGhC,IAAMC,UAAA,GAAcF,KAAA,CAAc,UAAUG,IAAA,CAAK,EAAEC,QAAA,CAAS,CAAC,MAAM,MAAM;AACzE,IAAIC,KAAA,GAAQ;AAEZ,SAASC,MAAMC,eAAA,EAAkC;EAC/C,MAAM,CAACC,EAAA,EAAIC,KAAK,IAAUT,KAAA,CAAAU,QAAA,CAA6BR,UAAA,CAAW,CAAC;EAEnED,eAAA,CAAgB,MAAM;IACpB,IAAI,CAACM,eAAA,EAAiBE,KAAA,CAAOE,OAAA,IAAYA,OAAA,IAAWC,MAAA,CAAOP,KAAA,EAAO,CAAC;EACrE,GAAG,CAACE,eAAe,CAAC;EACpB,OAAOA,eAAA,KAAoBC,EAAA,GAAK,SAASA,EAAE,KAAK;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}